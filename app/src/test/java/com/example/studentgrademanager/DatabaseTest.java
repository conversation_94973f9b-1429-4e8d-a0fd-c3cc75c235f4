package com.example.studentgrademanager;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 数据库功能测试
 * 验证基本的数据库操作功能
 */
public class DatabaseTest {

    @Test
    public void testStudentModel() {
        // 测试学生模型
        com.example.studentgrademanager.model.Student student = 
            new com.example.studentgrademanager.model.Student("2021001", "张三", "男", 20, "计算机1班", "13800138001", "<EMAIL>");
        
        assertEquals("2021001", student.getStudentId());
        assertEquals("张三", student.getName());
        assertEquals("男", student.getGender());
        assertEquals(20, student.getAge());
        assertEquals("计算机1班", student.getClassName());
        assertEquals("13800138001", student.getPhone());
        assertEquals("<EMAIL>", student.getEmail());
    }

    @Test
    public void testCourseModel() {
        // 测试课程模型
        com.example.studentgrademanager.model.Course course = 
            new com.example.studentgrademanager.model.Course("CS001", "Java程序设计", "刘老师", 4, "2024春季", "Java编程基础课程");
        
        assertEquals("CS001", course.getCourseId());
        assertEquals("Java程序设计", course.getCourseName());
        assertEquals("刘老师", course.getTeacher());
        assertEquals(4, course.getCredits());
        assertEquals("2024春季", course.getSemester());
        assertEquals("Java编程基础课程", course.getDescription());
    }

    @Test
    public void testGradeModel() {
        // 测试成绩模型
        com.example.studentgrademanager.model.Grade grade = 
            new com.example.studentgrademanager.model.Grade(1, 1, 85.5, "期中考试", "2024-04-15", "2024春季");
        
        assertEquals(1, grade.getStudentId());
        assertEquals(1, grade.getCourseId());
        assertEquals(85.5, grade.getScore(), 0.01);
        assertEquals("期中考试", grade.getExamType());
        assertEquals("2024-04-15", grade.getExamDate());
        assertEquals("2024春季", grade.getSemester());
    }
}
