package com.example.studentgrademanager.utils;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.OvershootInterpolator;
import android.widget.TextView;
import com.example.studentgrademanager.R;

/**
 * 自定义欢迎Toast
 * 带有气泡动画效果
 */
public class WelcomeToast {
    private Context context;
    private WindowManager windowManager;
    private View toastView;
    private boolean isShowing = false;

    public WelcomeToast(Context context) {
        this.context = context;
        this.windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
    }

    /**
     * 显示欢迎Toast
     */
    public void show(String message) {
        if (isShowing) {
            return;
        }

        // 创建Toast视图
        LayoutInflater inflater = LayoutInflater.from(context);
        toastView = inflater.inflate(R.layout.custom_welcome_toast, null);
        
        TextView textView = toastView.findViewById(R.id.tv_welcome_message);
        textView.setText(message);

        // 设置窗口参数
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                PixelFormat.TRANSLUCENT
        );

        params.gravity = Gravity.CENTER;
        params.x = 0;
        params.y = -100; // 稍微偏上显示

        // 初始状态设置为不可见
        toastView.setAlpha(0f);
        toastView.setScaleX(0.3f);
        toastView.setScaleY(0.3f);

        try {
            // 添加到窗口
            windowManager.addView(toastView, params);
            isShowing = true;

            // 开始动画
            startShowAnimation();
        } catch (Exception e) {
            e.printStackTrace();
            // 如果无法显示自定义Toast，使用系统Toast
            android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 开始显示动画
     */
    private void startShowAnimation() {
        // 透明度动画
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(toastView, "alpha", 0f, 1f);
        alphaAnimator.setDuration(300);

        // 缩放动画 - 气泡效果
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(toastView, "scaleX", 0.3f, 1.1f, 1f);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(toastView, "scaleY", 0.3f, 1.1f, 1f);
        scaleXAnimator.setDuration(500);
        scaleYAnimator.setDuration(500);
        scaleXAnimator.setInterpolator(new OvershootInterpolator(1.5f));
        scaleYAnimator.setInterpolator(new OvershootInterpolator(1.5f));

        // 开始动画
        alphaAnimator.start();
        scaleXAnimator.start();
        scaleYAnimator.start();

        // 延迟隐藏
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                startHideAnimation();
            }
        }, 2000);
    }

    /**
     * 开始隐藏动画
     */
    private void startHideAnimation() {
        if (!isShowing || toastView == null) {
            return;
        }

        // 透明度动画
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(toastView, "alpha", 1f, 0f);
        alphaAnimator.setDuration(300);

        // 缩放动画
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(toastView, "scaleX", 1f, 0.8f);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(toastView, "scaleY", 1f, 0.8f);
        scaleXAnimator.setDuration(300);
        scaleYAnimator.setDuration(300);

        alphaAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                hide();
            }
        });

        // 开始动画
        alphaAnimator.start();
        scaleXAnimator.start();
        scaleYAnimator.start();
    }

    /**
     * 隐藏Toast
     */
    private void hide() {
        if (isShowing && toastView != null) {
            try {
                windowManager.removeView(toastView);
            } catch (Exception e) {
                e.printStackTrace();
            }
            isShowing = false;
            toastView = null;
        }
    }
}
