package com.example.studentgrademanager.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.controller.LoginActivity;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;

/**
 * 退出登录帮助类
 * 提供统一的退出登录功能和确认对话框
 */
public class LogoutHelper {

    /**
     * 显示退出登录确认对话框
     */
    public static void showLogoutDialog(Activity activity) {
        SessionManager sessionManager = new SessionManager(activity);
        
        new MaterialAlertDialogBuilder(activity)
                .setTitle("退出登录")
                .setMessage("确定要退出登录吗？")
                .setIcon(R.drawable.ic_logout)
                .setPositiveButton("确定", (dialog, which) -> {
                    performLogout(activity, sessionManager);
                })
                .setNegativeButton("取消", (dialog, which) -> {
                    dialog.dismiss();
                })
                .setCancelable(true)
                .show();
    }

    /**
     * 直接执行退出登录（不显示确认对话框）
     */
    public static void performLogout(Activity activity) {
        SessionManager sessionManager = new SessionManager(activity);
        performLogout(activity, sessionManager);
    }

    /**
     * 执行退出登录操作
     */
    private static void performLogout(Activity activity, SessionManager sessionManager) {
        // 清除登录会话
        sessionManager.logoutUser();
        
        // 跳转到登录界面
        Intent intent = new Intent(activity, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        activity.startActivity(intent);
        activity.finish();
        
        // 显示退出成功提示
        Toast.makeText(activity, "已退出登录", Toast.LENGTH_SHORT).show();
    }

    /**
     * 检查是否已登录，如果未登录则跳转到登录界面
     */
    public static boolean checkLoginStatus(Activity activity) {
        SessionManager sessionManager = new SessionManager(activity);
        if (!sessionManager.isLoggedIn()) {
            Intent intent = new Intent(activity, LoginActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            activity.startActivity(intent);
            activity.finish();
            return false;
        }
        return true;
    }

    /**
     * 获取当前用户信息显示文本
     */
    public static String getCurrentUserDisplayText(Context context) {
        SessionManager sessionManager = new SessionManager(context);
        if (sessionManager.isLoggedIn()) {
            String realName = sessionManager.getCurrentRealName();
            String userType = sessionManager.isAdmin() ? "管理员" : "学生";
            return String.format("%s (%s)", realName, userType);
        }
        return "未登录";
    }
}
