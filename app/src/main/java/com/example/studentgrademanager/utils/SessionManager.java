package com.example.studentgrademanager.utils;

import android.content.Context;
import android.content.SharedPreferences;
import com.example.studentgrademanager.model.User;

/**
 * 会话管理类
 * 负责用户登录状态的保存和管理
 */
public class SessionManager {
    private static final String PREF_NAME = "UserSession";
    private static final String KEY_IS_LOGGED_IN = "isLoggedIn";
    private static final String KEY_USER_ID = "userId";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_USER_TYPE = "userType";
    private static final String KEY_REAL_NAME = "realName";
    private static final String KEY_STUDENT_ID = "studentId";

    private SharedPreferences pref;
    private SharedPreferences.Editor editor;
    private Context context;

    public SessionManager(Context context) {
        this.context = context;
        pref = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        editor = pref.edit();
    }

    /**
     * 创建登录会话
     */
    public void createLoginSession(User user) {
        editor.putBoolean(KEY_IS_LOGGED_IN, true);
        editor.putInt(KEY_USER_ID, user.getId());
        editor.putString(KEY_USERNAME, user.getUsername());
        editor.putString(KEY_USER_TYPE, user.getUserType());
        editor.putString(KEY_REAL_NAME, user.getRealName());
        editor.putInt(KEY_STUDENT_ID, user.getStudentId());
        editor.commit();
    }

    /**
     * 检查是否已登录
     */
    public boolean isLoggedIn() {
        return pref.getBoolean(KEY_IS_LOGGED_IN, false);
    }

    /**
     * 获取当前用户信息
     */
    public User getCurrentUser() {
        if (!isLoggedIn()) {
            return null;
        }

        User user = new User();
        user.setId(pref.getInt(KEY_USER_ID, 0));
        user.setUsername(pref.getString(KEY_USERNAME, ""));
        user.setUserType(pref.getString(KEY_USER_TYPE, ""));
        user.setRealName(pref.getString(KEY_REAL_NAME, ""));
        user.setStudentId(pref.getInt(KEY_STUDENT_ID, 0));
        return user;
    }

    /**
     * 获取当前用户ID
     */
    public int getCurrentUserId() {
        return pref.getInt(KEY_USER_ID, 0);
    }

    /**
     * 获取当前用户名
     */
    public String getCurrentUsername() {
        return pref.getString(KEY_USERNAME, "");
    }

    /**
     * 获取当前用户类型
     */
    public String getCurrentUserType() {
        return pref.getString(KEY_USER_TYPE, "");
    }

    /**
     * 获取当前用户真实姓名
     */
    public String getCurrentRealName() {
        return pref.getString(KEY_REAL_NAME, "");
    }

    /**
     * 获取关联的学生ID
     */
    public int getCurrentStudentId() {
        return pref.getInt(KEY_STUDENT_ID, 0);
    }

    /**
     * 判断当前用户是否为管理员
     */
    public boolean isAdmin() {
        return "admin".equals(getCurrentUserType());
    }

    /**
     * 判断当前用户是否为学生
     */
    public boolean isStudent() {
        return "student".equals(getCurrentUserType());
    }

    /**
     * 登出，清除会话
     */
    public void logoutUser() {
        editor.clear();
        editor.commit();
    }
}
