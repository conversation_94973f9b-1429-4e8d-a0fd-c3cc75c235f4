package com.example.studentgrademanager;
// 声明包名，确保类的唯一性和组织结构

import android.content.Intent;
// 用于Activity间跳转和数据传递
import android.os.Bundle;
// 用于Activity状态保存和恢复
import android.view.Menu;
// 用于创建选项菜单
import android.view.MenuItem;
// 用于处理菜单项点击事件
import android.view.View;
// 用于视图操作和事件处理
import android.widget.Button;
// 按钮控件类
import android.widget.Toast;
// 用于显示短暂提示信息
import androidx.appcompat.app.AppCompatActivity;
// 现代化Activity基类，支持ActionBar和Material Design
import com.example.studentgrademanager.controller.LoginActivity;
// 登录界面Activity
import com.example.studentgrademanager.controller.StudentManagementActivity;
// 学生管理界面Activity
import com.example.studentgrademanager.controller.CourseManagementActivity;
// 课程管理界面Activity
import com.example.studentgrademanager.controller.GradeManagementActivity;
// 成绩管理界面Activity
import com.example.studentgrademanager.controller.StatisticsActivity;
// 统计分析界面Activity
import com.example.studentgrademanager.utils.SessionManager;
// 会话管理工具类，用于用户状态跟踪和权限判断
import com.example.studentgrademanager.utils.LogoutHelper;
// 登出助手工具类，处理用户登出逻辑

/**
 * 主界面Activity - 学生成绩管理系统的核心控制器
 * 功能：提供系统主要功能模块的入口，实现权限控制和用户交互
 * 设计模式：MVC架构中的Controller层
 */
public class MainActivity extends AppCompatActivity {
// 继承AppCompatActivity获得现代化Activity功能，支持ActionBar、Fragment等

    // ==================== 视图组件声明 ====================
    private Button btnStudentManagement;
    // 学生管理功能按钮，管理员可进行CRUD操作，学生只能查看个人信息
    private Button btnCourseManagement;
    // 课程管理功能按钮，仅管理员可访问，学生用户此按钮将被隐藏
    private Button btnGradeManagement;
    // 成绩管理功能按钮，管理员可管理所有成绩，学生只能查看个人成绩
    private Button btnStatistics;
    // 统计分析功能按钮，管理员查看全局统计，学生查看个人统计
    private Button btnLogout;
    // 退出登录按钮，所有用户都可使用

    // ==================== 业务逻辑组件 ====================
    private SessionManager sessionManager;
    // 会话管理器：负责用户状态跟踪、权限判断、登录验证等核心安全功能

    // ==================== Activity生命周期方法 ====================
    @Override
    protected void onCreate(Bundle savedInstanceState) {
    // Activity创建时的主入口方法，负责初始化和安全检查
        super.onCreate(savedInstanceState);
        // 调用父类初始化方法，确保Activity基础功能正常运行
        setContentView(R.layout.activity_main);
        // 加载主界面布局文件，将XML布局转换为View对象树

        sessionManager = new SessionManager(this);
        // 创建会话管理器实例，用于后续的用户状态检查和权限验证

        // ==================== 安全验证：登录状态检查 ====================
        if (!sessionManager.isLoggedIn()) {
        // 检查用户是否已登录，防止未授权访问主界面
            redirectToLogin();
            // 未登录用户重定向到登录页面，确保系统安全性
            return;
            // 终止当前Activity初始化流程，防止未登录用户继续访问
        }

        // ==================== 界面初始化流程 ====================
        initViews();
        // 初始化所有视图组件，将XML中的控件绑定到Java对象
        setClickListeners();
        // 为所有按钮设置点击事件监听器，实现用户交互响应
        setupUserInterface();
        // 根据用户角色配置界面，实现个性化和权限控制
    }

    // ==================== 视图初始化方法 ====================
    /**
     * 初始化视图组件 - 将XML布局中的控件绑定到Java对象
     * 目的：建立XML视图与Java代码的连接，为后续操作做准备
     * 调用时机：onCreate中，布局文件加载完成后
     */
    private void initViews() {
        btnStudentManagement = findViewById(R.id.btn_student_management);
        // 绑定学生管理按钮，通过ID从布局文件中查找对应的Button控件
        btnCourseManagement = findViewById(R.id.btn_course_management);
        // 绑定课程管理按钮，管理员专用功能，学生用户将被隐藏
        btnGradeManagement = findViewById(R.id.btn_grade_management);
        // 绑定成绩管理按钮，支持权限控制的功能访问
        btnStatistics = findViewById(R.id.btn_statistics);
        // 绑定统计分析按钮，提供数据分析功能入口
        btnLogout = findViewById(R.id.btn_logout);
        // 绑定退出登录按钮，所有用户的通用功能
    }

    // ==================== 事件监听器设置方法 ====================
    /**
     * 设置点击事件监听器 - 为所有按钮配置响应机制
     * 目的：实现用户交互响应，包含权限检查和页面跳转逻辑
     * 特点：每个按钮都有独立的权限验证和响应处理
     */
    private void setClickListeners() {
        // -------------------- 学生管理按钮响应 --------------------
        btnStudentManagement.setOnClickListener(new View.OnClickListener() {
        // 设置学生管理按钮的点击监听器，支持权限分级访问
            @Override
            public void onClick(View v) {
            // 按钮点击时触发的回调方法，v参数为被点击的视图对象
                if (sessionManager.isAdmin()) {
                // 权限检查：判断当前用户是否为管理员
                    Intent intent = new Intent(MainActivity.this, StudentManagementActivity.class);
                    // 管理员模式：创建跳转意图，无额外限制参数
                    startActivity(intent);
                    // 启动学生管理Activity，管理员可进行完整的CRUD操作
                } else {
                // 学生用户分支：提供受限的功能访问
                    Intent intent = new Intent(MainActivity.this, StudentManagementActivity.class);
                    // 学生模式：创建相同的跳转意图，但会添加限制参数
                    intent.putExtra("student_only", true);
                    // 添加学生模式标记，目标Activity据此限制功能范围
                    intent.putExtra("student_id", sessionManager.getCurrentStudentId());
                    // 传递当前学生ID，确保只能查看自己的信息
                    startActivity(intent);
                    // 启动学生管理Activity，学生只能查看个人信息（只读模式）
                }
            }
        });

        // -------------------- 课程管理按钮响应 --------------------
        btnCourseManagement.setOnClickListener(new View.OnClickListener() {
        // 设置课程管理按钮监听器，实现严格的权限控制（仅管理员可访问）
            @Override
            public void onClick(View v) {
            // 课程管理按钮点击响应，采用权限拒绝策略
                if (sessionManager.isAdmin()) {
                // 管理员权限验证：只有管理员才能访问课程管理功能
                    Intent intent = new Intent(MainActivity.this, CourseManagementActivity.class);
                    // 创建课程管理Activity跳转意图，无需额外参数
                    startActivity(intent);
                    // 启动课程管理界面，管理员可进行课程的增删改查操作
                } else {
                // 学生用户权限拒绝分支：不允许访问课程管理
                    Toast.makeText(MainActivity.this, "学生用户无权限访问课程管理", Toast.LENGTH_SHORT).show();
                    // 显示权限不足提示，LENGTH_SHORT表示短暂显示，用户体验友好
                    // 注意：此处不执行startActivity()，完全阻止学生用户的访问
                }
            }
        });

        // -------------------- 成绩管理按钮响应 --------------------
        btnGradeManagement.setOnClickListener(new View.OnClickListener() {
        // 设置成绩管理按钮监听器，支持差异化权限访问
            @Override
            public void onClick(View v) {
            // 成绩管理按钮点击响应，管理员和学生都可访问但功能不同
                Intent intent = new Intent(MainActivity.this, GradeManagementActivity.class);
                // 创建成绩管理Activity跳转意图，所有用户都可以访问
                if (sessionManager.isStudent()) {
                // 学生用户权限检查：需要添加访问限制参数
                    intent.putExtra("student_only", true);
                    // 添加学生模式标记，限制只能查看个人成绩
                    intent.putExtra("student_id", sessionManager.getCurrentStudentId());
                    // 传递学生ID，确保数据访问范围限制在个人成绩内
                }
                // 管理员用户：无需添加额外参数，可以访问所有学生的成绩数据
                startActivity(intent);
                // 启动成绩管理Activity：管理员可管理所有成绩，学生只能查看个人成绩
            }
        });

        // -------------------- 统计分析按钮响应 --------------------
        btnStatistics.setOnClickListener(new View.OnClickListener() {
        // 设置统计分析按钮监听器，提供个性化的数据统计功能
            @Override
            public void onClick(View v) {
            // 统计分析按钮点击响应，根据用户角色提供不同范围的统计数据
                Intent intent = new Intent(MainActivity.this, StatisticsActivity.class);
                // 创建统计分析Activity跳转意图，所有用户都可以使用统计功能
                if (sessionManager.isStudent()) {
                // 学生用户：限制统计范围为个人数据
                    intent.putExtra("student_only", true);
                    // 添加个人统计模式标记，限制数据范围
                    intent.putExtra("student_id", sessionManager.getCurrentStudentId());
                    // 传递学生ID，确保只统计个人相关的成绩数据
                }
                // 管理员用户：无额外参数，可以查看全局统计数据
                startActivity(intent);
                // 启动统计分析Activity：管理员查看全校统计，学生查看个人统计
            }
        });

        // -------------------- 退出登录按钮响应 --------------------
        btnLogout.setOnClickListener(new View.OnClickListener() {
        // 设置退出登录按钮监听器，所有用户的通用功能
            @Override
            public void onClick(View v) {
            // 退出登录按钮点击响应，简洁的委托处理模式
                logout();
                // 调用logout方法处理登出逻辑，采用委托模式保持代码整洁
            }
        });
    }
    // setClickListeners方法结束，所有按钮的事件监听器设置完成

    // ==================== 用户界面配置方法 ====================
    /**
     * 设置用户界面 - 根据用户角色进行个性化配置
     * 目的：实现权限可视化，提供个性化用户体验
     * 特点：动态调整界面元素，隐藏无权限功能
     */
    private void setupUserInterface() {
        // -------------------- 权限可视化：界面元素动态调整 --------------------
        if (sessionManager.isStudent()) {
        // 学生用户界面适配：隐藏无权限访问的功能
            btnCourseManagement.setVisibility(View.GONE);
            // 隐藏课程管理按钮，View.GONE表示完全不可见且不占用布局空间
            // 这样学生用户就看不到无权限的功能，提升用户体验
        }
        // 管理员用户：保持所有按钮可见，无需特殊处理

        // -------------------- 个性化标题设置 --------------------
        setTitle("欢迎，" + sessionManager.getCurrentRealName());
        // 设置Activity标题，显示当前登录用户的真实姓名
        // 目的：增强用户归属感，确认当前登录身份，提升用户体验
    }

    // ==================== 页面跳转方法 ====================
    /**
     * 重定向到登录页面 - 安全机制的核心实现
     * 目的：处理未登录用户的访问，确保系统安全性
     * 特点：清除任务栈，防止用户通过返回键绕过登录验证
     */
    private void redirectToLogin() {
        Intent intent = new Intent(this, LoginActivity.class);
        // 创建登录Activity的跳转意图，准备重定向到登录界面
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        // 设置Intent标志：NEW_TASK创建新任务栈，CLEAR_TASK清除现有任务栈
        // 组合效果：确保用户无法通过返回键回到未授权的主界面
        startActivity(intent);
        // 启动登录Activity，用户需要重新进行身份验证
        finish();
        // 销毁当前MainActivity实例，释放内存资源，完成安全重定向
    }

    // ==================== 选项菜单系统 ====================
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
    // 创建选项菜单的回调方法，在ActionBar右侧显示菜单项
        getMenuInflater().inflate(R.menu.menu_main, menu);
        // 从XML资源文件加载菜单布局，将菜单项添加到ActionBar
        return true;
        // 返回true表示显示菜单，false则不显示
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
    // 处理选项菜单项点击事件的回调方法
        int id = item.getItemId();
        // 获取被点击菜单项的唯一标识ID
        if (id == R.id.action_logout) {
        // 检查是否点击了登出菜单项
            logout();
            // 调用登出方法，处理用户登出逻辑
            return true;
            // 返回true表示已处理该菜单项点击事件
        }
        return super.onOptionsItemSelected(item);
        // 其他菜单项交给父类处理，保持系统菜单功能正常
    }

    // ==================== 用户登出功能 ====================
    /**
     * 用户登出 - 安全退出系统的统一入口
     * 目的：提供安全的登出机制，清除用户会话信息
     * 设计模式：委托模式，将具体实现委托给LogoutHelper
     */
    private void logout() {
        LogoutHelper.showLogoutDialog(this);
        // 委托LogoutHelper处理登出逻辑：显示确认对话框、清除会话、跳转登录页
        // 采用委托模式的好处：MainActivity专注于界面控制，登出逻辑统一管理
    }
}
// MainActivity类结束 - 学生成绩管理系统主界面控制器实现完成
