package com.example.studentgrademanager.dao;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.example.studentgrademanager.database.DatabaseHelper;
import com.example.studentgrademanager.model.User;

/**
 * 用户数据访问对象
 * 负责用户表的CRUD操作和登录验证
 */
public class UserDAO {
    private DatabaseHelper dbHelper;
    private SQLiteDatabase database;

    public UserDAO(Context context) {
        dbHelper = new DatabaseHelper(context);
    }

    public void open() {
        database = dbHelper.getWritableDatabase();
    }

    public void close() {
        if (database != null && database.isOpen()) {
            database.close();
        }
    }

    /**
     * 用户登录验证
     */
    public User login(String username, String password) {
        Cursor cursor = database.query(DatabaseHelper.TABLE_USERS,
                null,
                DatabaseHelper.COLUMN_USER_USERNAME + " = ? AND " + DatabaseHelper.COLUMN_USER_PASSWORD + " = ?",
                new String[]{username, password},
                null, null, null);

        User user = null;
        if (cursor.moveToFirst()) {
            user = cursorToUser(cursor);
        }
        cursor.close();
        return user;
    }

    /**
     * 添加用户
     */
    public long addUser(User user) {
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_USER_USERNAME, user.getUsername());
        values.put(DatabaseHelper.COLUMN_USER_PASSWORD, user.getPassword());
        values.put(DatabaseHelper.COLUMN_USER_TYPE, user.getUserType());
        values.put(DatabaseHelper.COLUMN_USER_REAL_NAME, user.getRealName());
        values.put(DatabaseHelper.COLUMN_USER_STUDENT_ID, user.getStudentId());

        return database.insert(DatabaseHelper.TABLE_USERS, null, values);
    }

    /**
     * 根据用户名查询用户
     */
    public User getUserByUsername(String username) {
        Cursor cursor = database.query(DatabaseHelper.TABLE_USERS,
                null,
                DatabaseHelper.COLUMN_USER_USERNAME + " = ?",
                new String[]{username},
                null, null, null);

        User user = null;
        if (cursor.moveToFirst()) {
            user = cursorToUser(cursor);
        }
        cursor.close();
        return user;
    }

    /**
     * 根据学生ID查询用户
     */
    public User getUserByStudentId(int studentId) {
        Cursor cursor = database.query(DatabaseHelper.TABLE_USERS,
                null,
                DatabaseHelper.COLUMN_USER_STUDENT_ID + " = ?",
                new String[]{String.valueOf(studentId)},
                null, null, null);

        User user = null;
        if (cursor.moveToFirst()) {
            user = cursorToUser(cursor);
        }
        cursor.close();
        return user;
    }

    /**
     * 更新用户密码
     */
    public int updatePassword(String username, String newPassword) {
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_USER_PASSWORD, newPassword);

        return database.update(DatabaseHelper.TABLE_USERS, values,
                DatabaseHelper.COLUMN_USER_USERNAME + " = ?",
                new String[]{username});
    }

    /**
     * 将Cursor转换为User对象
     */
    private User cursorToUser(Cursor cursor) {
        User user = new User();
        user.setId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_USER_ID)));
        user.setUsername(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_USER_USERNAME)));
        user.setPassword(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_USER_PASSWORD)));
        user.setUserType(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_USER_TYPE)));
        user.setRealName(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_USER_REAL_NAME)));
        user.setStudentId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_USER_STUDENT_ID)));
        return user;
    }
}
