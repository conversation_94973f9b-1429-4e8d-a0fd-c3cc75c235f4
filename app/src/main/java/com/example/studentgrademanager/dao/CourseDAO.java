package com.example.studentgrademanager.dao;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.example.studentgrademanager.database.DatabaseHelper;
import com.example.studentgrademanager.model.Course;
import java.util.ArrayList;
import java.util.List;

/**
 * 课程数据访问对象
 * 负责课程表的CRUD操作
 */
public class CourseDAO {
    private DatabaseHelper dbHelper;
    private SQLiteDatabase database;

    public CourseDAO(Context context) {
        dbHelper = new DatabaseHelper(context);
    }

    public void open() {
        database = dbHelper.getWritableDatabase();
    }

    public void close() {
        if (database != null && database.isOpen()) {
            database.close();
        }
    }

    /**
     * 添加课程
     */
    public long addCourse(Course course) {
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_COURSE_NUMBER, course.getCourseId());
        values.put(DatabaseHelper.COLUMN_COURSE_NAME, course.getCourseName());
        values.put(DatabaseHelper.COLUMN_COURSE_TEACHER, course.getTeacher());
        values.put(DatabaseHelper.COLUMN_COURSE_CREDITS, course.getCredits());
        values.put(DatabaseHelper.COLUMN_COURSE_SEMESTER, course.getSemester());
        values.put(DatabaseHelper.COLUMN_COURSE_DESCRIPTION, course.getDescription());

        return database.insert(DatabaseHelper.TABLE_COURSES, null, values);
    }

    /**
     * 删除课程
     */
    public int deleteCourse(int id) {
        return database.delete(DatabaseHelper.TABLE_COURSES,
                DatabaseHelper.COLUMN_COURSE_ID + " = ?",
                new String[]{String.valueOf(id)});
    }

    /**
     * 更新课程信息
     */
    public int updateCourse(Course course) {
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_COURSE_NUMBER, course.getCourseId());
        values.put(DatabaseHelper.COLUMN_COURSE_NAME, course.getCourseName());
        values.put(DatabaseHelper.COLUMN_COURSE_TEACHER, course.getTeacher());
        values.put(DatabaseHelper.COLUMN_COURSE_CREDITS, course.getCredits());
        values.put(DatabaseHelper.COLUMN_COURSE_SEMESTER, course.getSemester());
        values.put(DatabaseHelper.COLUMN_COURSE_DESCRIPTION, course.getDescription());

        return database.update(DatabaseHelper.TABLE_COURSES, values,
                DatabaseHelper.COLUMN_COURSE_ID + " = ?",
                new String[]{String.valueOf(course.getId())});
    }

    /**
     * 根据ID查询课程
     */
    public Course getCourseById(int id) {
        Cursor cursor = database.query(DatabaseHelper.TABLE_COURSES,
                null,
                DatabaseHelper.COLUMN_COURSE_ID + " = ?",
                new String[]{String.valueOf(id)},
                null, null, null);

        Course course = null;
        if (cursor.moveToFirst()) {
            course = cursorToCourse(cursor);
        }
        cursor.close();
        return course;
    }

    /**
     * 根据课程编号查询课程
     */
    public Course getCourseByNumber(String courseNumber) {
        Cursor cursor = database.query(DatabaseHelper.TABLE_COURSES,
                null,
                DatabaseHelper.COLUMN_COURSE_NUMBER + " = ?",
                new String[]{courseNumber},
                null, null, null);

        Course course = null;
        if (cursor.moveToFirst()) {
            course = cursorToCourse(cursor);
        }
        cursor.close();
        return course;
    }

    /**
     * 获取所有课程
     */
    public List<Course> getAllCourses() {
        List<Course> courses = new ArrayList<>();
        Cursor cursor = database.query(DatabaseHelper.TABLE_COURSES,
                null, null, null, null, null,
                DatabaseHelper.COLUMN_COURSE_NAME + " ASC");

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Course course = cursorToCourse(cursor);
            courses.add(course);
            cursor.moveToNext();
        }
        cursor.close();
        return courses;
    }

    /**
     * 根据学期查询课程
     */
    public List<Course> getCoursesBySemester(String semester) {
        List<Course> courses = new ArrayList<>();
        Cursor cursor = database.query(DatabaseHelper.TABLE_COURSES,
                null,
                DatabaseHelper.COLUMN_COURSE_SEMESTER + " = ?",
                new String[]{semester},
                null, null,
                DatabaseHelper.COLUMN_COURSE_NAME + " ASC");

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Course course = cursorToCourse(cursor);
            courses.add(course);
            cursor.moveToNext();
        }
        cursor.close();
        return courses;
    }

    /**
     * 根据教师查询课程
     */
    public List<Course> getCoursesByTeacher(String teacher) {
        List<Course> courses = new ArrayList<>();
        Cursor cursor = database.query(DatabaseHelper.TABLE_COURSES,
                null,
                DatabaseHelper.COLUMN_COURSE_TEACHER + " = ?",
                new String[]{teacher},
                null, null,
                DatabaseHelper.COLUMN_COURSE_NAME + " ASC");

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Course course = cursorToCourse(cursor);
            courses.add(course);
            cursor.moveToNext();
        }
        cursor.close();
        return courses;
    }

    /**
     * 搜索课程（根据课程名称或课程编号）
     */
    public List<Course> searchCourses(String keyword) {
        List<Course> courses = new ArrayList<>();
        String selection = DatabaseHelper.COLUMN_COURSE_NAME + " LIKE ? OR " +
                          DatabaseHelper.COLUMN_COURSE_NUMBER + " LIKE ?";
        String[] selectionArgs = {"%" + keyword + "%", "%" + keyword + "%"};

        Cursor cursor = database.query(DatabaseHelper.TABLE_COURSES,
                null, selection, selectionArgs, null, null,
                DatabaseHelper.COLUMN_COURSE_NAME + " ASC");

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Course course = cursorToCourse(cursor);
            courses.add(course);
            cursor.moveToNext();
        }
        cursor.close();
        return courses;
    }



    /**
     * 获取所有学期列表
     */
    public List<String> getAllSemesters() {
        List<String> semesters = new ArrayList<>();
        Cursor cursor = database.query(true, DatabaseHelper.TABLE_COURSES,
                new String[]{DatabaseHelper.COLUMN_COURSE_SEMESTER},
                null, null, null, null,
                DatabaseHelper.COLUMN_COURSE_SEMESTER + " ASC", null);

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            String semester = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COURSE_SEMESTER));
            semesters.add(semester);
            cursor.moveToNext();
        }
        cursor.close();
        return semesters;
    }

    /**
     * 获取所有教师列表
     */
    public List<String> getAllTeachers() {
        List<String> teachers = new ArrayList<>();
        Cursor cursor = database.query(true, DatabaseHelper.TABLE_COURSES,
                new String[]{DatabaseHelper.COLUMN_COURSE_TEACHER},
                null, null, null, null,
                DatabaseHelper.COLUMN_COURSE_TEACHER + " ASC", null);

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            String teacher = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COURSE_TEACHER));
            teachers.add(teacher);
            cursor.moveToNext();
        }
        cursor.close();
        return teachers;
    }

    /**
     * 将Cursor转换为Course对象
     */
    private Course cursorToCourse(Cursor cursor) {
        Course course = new Course();
        course.setId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COURSE_ID)));
        course.setCourseId(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COURSE_NUMBER)));
        course.setCourseName(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COURSE_NAME)));
        course.setTeacher(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COURSE_TEACHER)));
        course.setCredits(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COURSE_CREDITS)));
        course.setSemester(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COURSE_SEMESTER)));
        course.setDescription(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COURSE_DESCRIPTION)));
        return course;
    }
}
