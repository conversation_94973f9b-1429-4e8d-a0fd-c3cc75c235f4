package com.example.studentgrademanager.dao;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.example.studentgrademanager.database.DatabaseHelper;
import com.example.studentgrademanager.model.Grade;
import java.util.ArrayList;
import java.util.List;

/**
 * 成绩数据访问对象
 * 负责成绩表的CRUD操作，支持多表联动查询
 */
public class GradeDAO {
    private DatabaseHelper dbHelper;
    private SQLiteDatabase database;

    public GradeDAO(Context context) {
        dbHelper = new DatabaseHelper(context);
    }

    public void open() {
        database = dbHelper.getWritableDatabase();
    }

    public void close() {
        if (database != null && database.isOpen()) {
            database.close();
        }
    }

    /**
     * 添加成绩
     */
    public long addGrade(Grade grade) {
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_GRADE_STUDENT_ID, grade.getStudentId());
        values.put(DatabaseHelper.COLUMN_GRADE_COURSE_ID, grade.getCourseId());
        values.put(DatabaseHelper.COLUMN_GRADE_SCORE, grade.getScore());
        values.put(DatabaseHelper.COLUMN_GRADE_EXAM_TYPE, grade.getExamType());
        values.put(DatabaseHelper.COLUMN_GRADE_EXAM_DATE, grade.getExamDate());
        values.put(DatabaseHelper.COLUMN_GRADE_SEMESTER, grade.getSemester());

        return database.insert(DatabaseHelper.TABLE_GRADES, null, values);
    }

    /**
     * 删除成绩
     */
    public int deleteGrade(int id) {
        return database.delete(DatabaseHelper.TABLE_GRADES,
                DatabaseHelper.COLUMN_GRADE_ID + " = ?",
                new String[]{String.valueOf(id)});
    }

    /**
     * 更新成绩信息
     */
    public int updateGrade(Grade grade) {
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_GRADE_STUDENT_ID, grade.getStudentId());
        values.put(DatabaseHelper.COLUMN_GRADE_COURSE_ID, grade.getCourseId());
        values.put(DatabaseHelper.COLUMN_GRADE_SCORE, grade.getScore());
        values.put(DatabaseHelper.COLUMN_GRADE_EXAM_TYPE, grade.getExamType());
        values.put(DatabaseHelper.COLUMN_GRADE_EXAM_DATE, grade.getExamDate());
        values.put(DatabaseHelper.COLUMN_GRADE_SEMESTER, grade.getSemester());

        return database.update(DatabaseHelper.TABLE_GRADES, values,
                DatabaseHelper.COLUMN_GRADE_ID + " = ?",
                new String[]{String.valueOf(grade.getId())});
    }

    /**
     * 根据ID查询成绩
     */
    public Grade getGradeById(int id) {
        String sql = "SELECT g.*, s.name as student_name, c.course_name " +
                    "FROM " + DatabaseHelper.TABLE_GRADES + " g " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_STUDENTS + " s ON g.student_id = s.id " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_COURSES + " c ON g.course_id = c.id " +
                    "WHERE g.id = ?";

        Cursor cursor = database.rawQuery(sql, new String[]{String.valueOf(id)});

        Grade grade = null;
        if (cursor.moveToFirst()) {
            grade = cursorToGrade(cursor);
        }
        cursor.close();
        return grade;
    }

    /**
     * 获取所有成绩（包含学生姓名和课程名称）
     */
    public List<Grade> getAllGrades() {
        List<Grade> grades = new ArrayList<>();
        String sql = "SELECT g.*, s.name as student_name, c.course_name " +
                    "FROM " + DatabaseHelper.TABLE_GRADES + " g " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_STUDENTS + " s ON g.student_id = s.id " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_COURSES + " c ON g.course_id = c.id " +
                    "ORDER BY g.exam_date DESC";

        Cursor cursor = database.rawQuery(sql, null);

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Grade grade = cursorToGrade(cursor);
            grades.add(grade);
            cursor.moveToNext();
        }
        cursor.close();
        return grades;
    }

    /**
     * 根据学生ID查询成绩
     */
    public List<Grade> getGradesByStudentId(int studentId) {
        List<Grade> grades = new ArrayList<>();
        String sql = "SELECT g.*, s.name as student_name, c.course_name " +
                    "FROM " + DatabaseHelper.TABLE_GRADES + " g " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_STUDENTS + " s ON g.student_id = s.id " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_COURSES + " c ON g.course_id = c.id " +
                    "WHERE g.student_id = ? " +
                    "ORDER BY g.exam_date DESC";

        Cursor cursor = database.rawQuery(sql, new String[]{String.valueOf(studentId)});

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Grade grade = cursorToGrade(cursor);
            grades.add(grade);
            cursor.moveToNext();
        }
        cursor.close();
        return grades;
    }

    /**
     * 根据课程ID查询成绩
     */
    public List<Grade> getGradesByCourseId(int courseId) {
        List<Grade> grades = new ArrayList<>();
        String sql = "SELECT g.*, s.name as student_name, c.course_name " +
                    "FROM " + DatabaseHelper.TABLE_GRADES + " g " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_STUDENTS + " s ON g.student_id = s.id " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_COURSES + " c ON g.course_id = c.id " +
                    "WHERE g.course_id = ? " +
                    "ORDER BY g.score DESC";

        Cursor cursor = database.rawQuery(sql, new String[]{String.valueOf(courseId)});

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Grade grade = cursorToGrade(cursor);
            grades.add(grade);
            cursor.moveToNext();
        }
        cursor.close();
        return grades;
    }

    /**
     * 根据学期查询成绩
     */
    public List<Grade> getGradesBySemester(String semester) {
        List<Grade> grades = new ArrayList<>();
        String sql = "SELECT g.*, s.name as student_name, c.course_name " +
                    "FROM " + DatabaseHelper.TABLE_GRADES + " g " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_STUDENTS + " s ON g.student_id = s.id " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_COURSES + " c ON g.course_id = c.id " +
                    "WHERE g.semester = ? " +
                    "ORDER BY g.score DESC";

        Cursor cursor = database.rawQuery(sql, new String[]{semester});

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Grade grade = cursorToGrade(cursor);
            grades.add(grade);
            cursor.moveToNext();
        }
        cursor.close();
        return grades;
    }

    /**
     * 计算学生平均分
     */
    public double getStudentAverageScore(int studentId) {
        String sql = "SELECT AVG(score) as avg_score FROM " + DatabaseHelper.TABLE_GRADES +
                    " WHERE student_id = ?";
        Cursor cursor = database.rawQuery(sql, new String[]{String.valueOf(studentId)});

        double avgScore = 0.0;
        if (cursor.moveToFirst()) {
            avgScore = cursor.getDouble(0);
        }
        cursor.close();
        return avgScore;
    }

    /**
     * 计算课程平均分
     */
    public double getCourseAverageScore(int courseId) {
        String sql = "SELECT AVG(score) as avg_score FROM " + DatabaseHelper.TABLE_GRADES +
                    " WHERE course_id = ?";
        Cursor cursor = database.rawQuery(sql, new String[]{String.valueOf(courseId)});

        double avgScore = 0.0;
        if (cursor.moveToFirst()) {
            avgScore = cursor.getDouble(0);
        }
        cursor.close();
        return avgScore;
    }

    /**
     * 获取成绩统计信息
     */
    public List<Grade> getGradeStatistics() {
        List<Grade> statistics = new ArrayList<>();
        String sql = "SELECT s.name as student_name, AVG(g.score) as avg_score, " +
                    "COUNT(g.id) as total_courses, MAX(g.score) as max_score, MIN(g.score) as min_score " +
                    "FROM " + DatabaseHelper.TABLE_GRADES + " g " +
                    "LEFT JOIN " + DatabaseHelper.TABLE_STUDENTS + " s ON g.student_id = s.id " +
                    "GROUP BY g.student_id, s.name " +
                    "ORDER BY avg_score DESC";

        Cursor cursor = database.rawQuery(sql, null);

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Grade stat = new Grade();
            stat.setStudentName(cursor.getString(cursor.getColumnIndexOrThrow("student_name")));
            stat.setScore(cursor.getDouble(cursor.getColumnIndexOrThrow("avg_score")));
            statistics.add(stat);
            cursor.moveToNext();
        }
        cursor.close();
        return statistics;
    }



    /**
     * 将Cursor转换为Grade对象
     */
    private Grade cursorToGrade(Cursor cursor) {
        Grade grade = new Grade();
        grade.setId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_GRADE_ID)));
        grade.setStudentId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_GRADE_STUDENT_ID)));
        grade.setCourseId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_GRADE_COURSE_ID)));
        grade.setScore(cursor.getDouble(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_GRADE_SCORE)));
        grade.setExamType(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_GRADE_EXAM_TYPE)));
        grade.setExamDate(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_GRADE_EXAM_DATE)));
        grade.setSemester(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_GRADE_SEMESTER)));

        // 设置关联的学生姓名和课程名称
        try {
            grade.setStudentName(cursor.getString(cursor.getColumnIndexOrThrow("student_name")));
            grade.setCourseName(cursor.getString(cursor.getColumnIndexOrThrow("course_name")));
        } catch (IllegalArgumentException e) {
            // 如果没有这些列，忽略错误
        }

        return grade;
    }
}
