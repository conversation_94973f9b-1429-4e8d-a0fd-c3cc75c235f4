package com.example.studentgrademanager.dao;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.example.studentgrademanager.database.DatabaseHelper;
import com.example.studentgrademanager.model.Student;
import java.util.ArrayList;
import java.util.List;

/**
 * 学生数据访问对象
 * 负责学生表的CRUD操作
 */
public class StudentDAO {
    private DatabaseHelper dbHelper;
    private SQLiteDatabase database;

    public StudentDAO(Context context) {
        dbHelper = new DatabaseHelper(context);
    }

    public void open() {
        database = dbHelper.getWritableDatabase();
    }

    public void close() {
        if (database != null && database.isOpen()) {
            database.close();
        }
    }

    /**
     * 添加学生
     */
    public long addStudent(Student student) {
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_STUDENT_NUMBER, student.getStudentId());
        values.put(DatabaseHelper.COLUMN_STUDENT_NAME, student.getName());
        values.put(DatabaseHelper.COLUMN_STUDENT_GENDER, student.getGender());
        values.put(DatabaseHelper.COLUMN_STUDENT_AGE, student.getAge());
        values.put(DatabaseHelper.COLUMN_STUDENT_CLASS, student.getClassName());
        values.put(DatabaseHelper.COLUMN_STUDENT_PHONE, student.getPhone());
        values.put(DatabaseHelper.COLUMN_STUDENT_EMAIL, student.getEmail());

        return database.insert(DatabaseHelper.TABLE_STUDENTS, null, values);
    }

    /**
     * 删除学生
     */
    public int deleteStudent(int id) {
        return database.delete(DatabaseHelper.TABLE_STUDENTS,
                DatabaseHelper.COLUMN_STUDENT_ID + " = ?",
                new String[]{String.valueOf(id)});
    }

    /**
     * 更新学生信息
     */
    public int updateStudent(Student student) {
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_STUDENT_NUMBER, student.getStudentId());
        values.put(DatabaseHelper.COLUMN_STUDENT_NAME, student.getName());
        values.put(DatabaseHelper.COLUMN_STUDENT_GENDER, student.getGender());
        values.put(DatabaseHelper.COLUMN_STUDENT_AGE, student.getAge());
        values.put(DatabaseHelper.COLUMN_STUDENT_CLASS, student.getClassName());
        values.put(DatabaseHelper.COLUMN_STUDENT_PHONE, student.getPhone());
        values.put(DatabaseHelper.COLUMN_STUDENT_EMAIL, student.getEmail());

        return database.update(DatabaseHelper.TABLE_STUDENTS, values,
                DatabaseHelper.COLUMN_STUDENT_ID + " = ?",
                new String[]{String.valueOf(student.getId())});
    }

    /**
     * 根据ID查询学生
     */
    public Student getStudentById(int id) {
        Cursor cursor = database.query(DatabaseHelper.TABLE_STUDENTS,
                null,
                DatabaseHelper.COLUMN_STUDENT_ID + " = ?",
                new String[]{String.valueOf(id)},
                null, null, null);

        Student student = null;
        if (cursor.moveToFirst()) {
            student = cursorToStudent(cursor);
        }
        cursor.close();
        return student;
    }

    /**
     * 根据学号查询学生
     */
    public Student getStudentByNumber(String studentNumber) {
        Cursor cursor = database.query(DatabaseHelper.TABLE_STUDENTS,
                null,
                DatabaseHelper.COLUMN_STUDENT_NUMBER + " = ?",
                new String[]{studentNumber},
                null, null, null);

        Student student = null;
        if (cursor.moveToFirst()) {
            student = cursorToStudent(cursor);
        }
        cursor.close();
        return student;
    }

    /**
     * 获取所有学生
     */
    public List<Student> getAllStudents() {
        List<Student> students = new ArrayList<>();
        Cursor cursor = database.query(DatabaseHelper.TABLE_STUDENTS,
                null, null, null, null, null,
                DatabaseHelper.COLUMN_STUDENT_NAME + " ASC");

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Student student = cursorToStudent(cursor);
            students.add(student);
            cursor.moveToNext();
        }
        cursor.close();
        return students;
    }

    /**
     * 根据班级查询学生
     */
    public List<Student> getStudentsByClass(String className) {
        List<Student> students = new ArrayList<>();
        Cursor cursor = database.query(DatabaseHelper.TABLE_STUDENTS,
                null,
                DatabaseHelper.COLUMN_STUDENT_CLASS + " = ?",
                new String[]{className},
                null, null,
                DatabaseHelper.COLUMN_STUDENT_NAME + " ASC");

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Student student = cursorToStudent(cursor);
            students.add(student);
            cursor.moveToNext();
        }
        cursor.close();
        return students;
    }

    /**
     * 搜索学生（根据姓名或学号）
     */
    public List<Student> searchStudents(String keyword) {
        List<Student> students = new ArrayList<>();
        String selection = DatabaseHelper.COLUMN_STUDENT_NAME + " LIKE ? OR " +
                          DatabaseHelper.COLUMN_STUDENT_NUMBER + " LIKE ?";
        String[] selectionArgs = {"%" + keyword + "%", "%" + keyword + "%"};

        Cursor cursor = database.query(DatabaseHelper.TABLE_STUDENTS,
                null, selection, selectionArgs, null, null,
                DatabaseHelper.COLUMN_STUDENT_NAME + " ASC");

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            Student student = cursorToStudent(cursor);
            students.add(student);
            cursor.moveToNext();
        }
        cursor.close();
        return students;
    }

    /**
     * 获取所有班级列表
     */
    public List<String> getAllClasses() {
        List<String> classes = new ArrayList<>();
        Cursor cursor = database.query(true, DatabaseHelper.TABLE_STUDENTS,
                new String[]{DatabaseHelper.COLUMN_STUDENT_CLASS},
                null, null, null, null,
                DatabaseHelper.COLUMN_STUDENT_CLASS + " ASC", null);

        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            String className = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_STUDENT_CLASS));
            classes.add(className);
            cursor.moveToNext();
        }
        cursor.close();
        return classes;
    }

    /**
     * 将Cursor转换为Student对象
     */
    private Student cursorToStudent(Cursor cursor) {
        Student student = new Student();
        student.setId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_STUDENT_ID)));
        student.setStudentId(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_STUDENT_NUMBER)));
        student.setName(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_STUDENT_NAME)));
        student.setGender(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_STUDENT_GENDER)));
        student.setAge(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_STUDENT_AGE)));
        student.setClassName(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_STUDENT_CLASS)));
        student.setPhone(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_STUDENT_PHONE)));
        student.setEmail(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_STUDENT_EMAIL)));
        return student;
    }
}
