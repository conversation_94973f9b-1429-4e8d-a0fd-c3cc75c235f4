package com.example.studentgrademanager.model;

/**
 * 成绩实体类
 * 用于表示学生的成绩信息
 */
public class Grade {
    private int id;
    private int studentId;     // 学生ID（外键）
    private int courseId;      // 课程ID（外键）
    private String studentName; // 学生姓名（冗余字段，便于显示）
    private String courseName;  // 课程名称（冗余字段，便于显示）
    private double score;       // 成绩
    private String examType;    // 考试类型（期中、期末、平时等）
    private String examDate;    // 考试日期
    private String semester;    // 学期

    // 构造函数
    public Grade() {}

    public Grade(int studentId, int courseId, double score, String examType, String examDate, String semester) {
        this.studentId = studentId;
        this.courseId = courseId;
        this.score = score;
        this.examType = examType;
        this.examDate = examDate;
        this.semester = semester;
    }

    public Grade(int id, int studentId, int courseId, String studentName, String courseName, 
                 double score, String examType, String examDate, String semester) {
        this.id = id;
        this.studentId = studentId;
        this.courseId = courseId;
        this.studentName = studentName;
        this.courseName = courseName;
        this.score = score;
        this.examType = examType;
        this.examDate = examDate;
        this.semester = semester;
    }

    // Getter和Setter方法
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getStudentId() {
        return studentId;
    }

    public void setStudentId(int studentId) {
        this.studentId = studentId;
    }

    public int getCourseId() {
        return courseId;
    }

    public void setCourseId(int courseId) {
        this.courseId = courseId;
    }

    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public String getExamType() {
        return examType;
    }

    public void setExamType(String examType) {
        this.examType = examType;
    }

    public String getExamDate() {
        return examDate;
    }

    public void setExamDate(String examDate) {
        this.examDate = examDate;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    @Override
    public String toString() {
        return "Grade{" +
                "id=" + id +
                ", studentId=" + studentId +
                ", courseId=" + courseId +
                ", studentName='" + studentName + '\'' +
                ", courseName='" + courseName + '\'' +
                ", score=" + score +
                ", examType='" + examType + '\'' +
                ", examDate='" + examDate + '\'' +
                ", semester='" + semester + '\'' +
                '}';
    }
}
