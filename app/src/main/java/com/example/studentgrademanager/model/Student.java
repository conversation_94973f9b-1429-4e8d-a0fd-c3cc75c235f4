package com.example.studentgrademanager.model;

/**
 * 学生实体类
 * 用于表示学生的基本信息
 */
public class Student {
    private int id;
    private String studentId;  // 学号
    private String name;       // 姓名
    private String gender;     // 性别
    private int age;          // 年龄
    private String className; // 班级
    private String phone;     // 电话
    private String email;     // 邮箱

    // 构造函数
    public Student() {}

    public Student(String studentId, String name, String gender, int age, String className, String phone, String email) {
        this.studentId = studentId;
        this.name = name;
        this.gender = gender;
        this.age = age;
        this.className = className;
        this.phone = phone;
        this.email = email;
    }

    public Student(int id, String studentId, String name, String gender, int age, String className, String phone, String email) {
        this.id = id;
        this.studentId = studentId;
        this.name = name;
        this.gender = gender;
        this.age = age;
        this.className = className;
        this.phone = phone;
        this.email = email;
    }

    // Getter和Setter方法
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getStudentId() {
        return studentId;
    }

    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String toString() {
        return "Student{" +
                "id=" + id +
                ", studentId='" + studentId + '\'' +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", age=" + age +
                ", className='" + className + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
