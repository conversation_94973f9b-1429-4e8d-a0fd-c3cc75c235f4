package com.example.studentgrademanager.model;

/**
 * 用户实体类
 * 用于表示系统用户信息（管理员和学生）
 */
public class User {
    private int id;
    private String username;    // 用户名
    private String password;    // 密码
    private String userType;    // 用户类型：admin(管理员) 或 student(学生)
    private String realName;    // 真实姓名
    private int studentId;      // 关联的学生ID（仅学生用户有效）

    // 构造函数
    public User() {}

    public User(String username, String password, String userType, String realName) {
        this.username = username;
        this.password = password;
        this.userType = userType;
        this.realName = realName;
    }

    public User(String username, String password, String userType, String realName, int studentId) {
        this.username = username;
        this.password = password;
        this.userType = userType;
        this.realName = realName;
        this.studentId = studentId;
    }

    public User(int id, String username, String password, String userType, String realName, int studentId) {
        this.id = id;
        this.username = username;
        this.password = password;
        this.userType = userType;
        this.realName = realName;
        this.studentId = studentId;
    }

    // Getter和Setter方法
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public int getStudentId() {
        return studentId;
    }

    public void setStudentId(int studentId) {
        this.studentId = studentId;
    }

    /**
     * 判断是否为管理员
     */
    public boolean isAdmin() {
        return "admin".equals(userType);
    }

    /**
     * 判断是否为学生
     */
    public boolean isStudent() {
        return "student".equals(userType);
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", userType='" + userType + '\'' +
                ", realName='" + realName + '\'' +
                ", studentId=" + studentId +
                '}';
    }
}
