package com.example.studentgrademanager.model;

/**
 * 课程实体类
 * 用于表示课程的基本信息
 */
public class Course {
    private int id;
    private String courseId;    // 课程编号
    private String courseName;  // 课程名称
    private String teacher;     // 授课教师
    private int credits;        // 学分
    private String semester;    // 学期
    private String description; // 课程描述

    // 构造函数
    public Course() {}

    public Course(String courseId, String courseName, String teacher, int credits, String semester, String description) {
        this.courseId = courseId;
        this.courseName = courseName;
        this.teacher = teacher;
        this.credits = credits;
        this.semester = semester;
        this.description = description;
    }

    public Course(int id, String courseId, String courseName, String teacher, int credits, String semester, String description) {
        this.id = id;
        this.courseId = courseId;
        this.courseName = courseName;
        this.teacher = teacher;
        this.credits = credits;
        this.semester = semester;
        this.description = description;
    }

    // Getter和Setter方法
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getTeacher() {
        return teacher;
    }

    public void setTeacher(String teacher) {
        this.teacher = teacher;
    }

    public int getCredits() {
        return credits;
    }

    public void setCredits(int credits) {
        this.credits = credits;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "Course{" +
                "id=" + id +
                ", courseId='" + courseId + '\'' +
                ", courseName='" + courseName + '\'' +
                ", teacher='" + teacher + '\'' +
                ", credits=" + credits +
                ", semester='" + semester + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
