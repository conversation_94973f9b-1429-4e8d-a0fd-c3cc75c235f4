package com.example.studentgrademanager.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.model.Grade;
import java.util.List;

/**
 * 统计数据适配器
 * 用于RecyclerView显示统计信息
 */
public class StatisticsAdapter extends RecyclerView.Adapter<StatisticsAdapter.StatisticsViewHolder> {

    private List<Grade> statisticsList;

    public StatisticsAdapter(List<Grade> statisticsList) {
        this.statisticsList = statisticsList;
    }

    @NonNull
    @Override
    public StatisticsViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_statistics, parent, false);
        return new StatisticsViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull StatisticsViewHolder holder, int position) {
        Grade statistics = statisticsList.get(position);
        holder.bind(statistics, position + 1);
    }

    @Override
    public int getItemCount() {
        return statisticsList.size();
    }

    /**
     * 更新数据
     */
    public void updateData(List<Grade> newStatisticsList) {
        this.statisticsList = newStatisticsList;
        notifyDataSetChanged();
    }

    class StatisticsViewHolder extends RecyclerView.ViewHolder {
        private TextView tvRank, tvStudentName, tvAverageScore;

        public StatisticsViewHolder(@NonNull View itemView) {
            super(itemView);
            tvRank = itemView.findViewById(R.id.tv_rank);
            tvStudentName = itemView.findViewById(R.id.tv_student_name);
            tvAverageScore = itemView.findViewById(R.id.tv_average_score);
        }

        public void bind(Grade statistics, int rank) {
            tvRank.setText(String.valueOf(rank));
            tvStudentName.setText(statistics.getStudentName());
            tvAverageScore.setText(String.format("%.2f", statistics.getScore()));
        }
    }
}
