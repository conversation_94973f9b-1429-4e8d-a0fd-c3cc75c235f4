package com.example.studentgrademanager.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.model.Grade;
import java.util.List;

/**
 * 成绩列表适配器
 * 用于RecyclerView显示成绩信息
 */
public class GradeAdapter extends RecyclerView.Adapter<GradeAdapter.GradeViewHolder> {

    private List<Grade> gradeList;
    private OnGradeClickListener listener;
    private boolean isStudentOnly;

    public interface OnGradeClickListener {
        void onGradeClick(Grade grade);
        void onGradeDelete(Grade grade);
    }

    public GradeAdapter(List<Grade> gradeList, OnGradeClickListener listener) {
        this.gradeList = gradeList;
        this.listener = listener;
        this.isStudentOnly = false;
    }

    public GradeAdapter(List<Grade> gradeList, OnGradeClickListener listener, boolean isStudentOnly) {
        this.gradeList = gradeList;
        this.listener = listener;
        this.isStudentOnly = isStudentOnly;
    }

    @NonNull
    @Override
    public GradeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_grade, parent, false);
        return new GradeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull GradeViewHolder holder, int position) {
        Grade grade = gradeList.get(position);
        holder.bind(grade);
    }

    @Override
    public int getItemCount() {
        return gradeList.size();
    }

    /**
     * 更新数据
     */
    public void updateData(List<Grade> newGradeList) {
        this.gradeList = newGradeList;
        notifyDataSetChanged();
    }

    class GradeViewHolder extends RecyclerView.ViewHolder {
        private TextView tvStudentName, tvCourseName, tvScore, tvExamType, tvExamDate, tvSemester;
        private Button btnEdit, btnDelete;

        public GradeViewHolder(@NonNull View itemView) {
            super(itemView);
            tvStudentName = itemView.findViewById(R.id.tv_student_name);
            tvCourseName = itemView.findViewById(R.id.tv_course_name);
            tvScore = itemView.findViewById(R.id.tv_score);
            tvExamType = itemView.findViewById(R.id.tv_exam_type);
            tvExamDate = itemView.findViewById(R.id.tv_exam_date);
            tvSemester = itemView.findViewById(R.id.tv_semester);
            btnEdit = itemView.findViewById(R.id.btn_edit_grade);
            btnDelete = itemView.findViewById(R.id.btn_delete_grade);
        }

        public void bind(Grade grade) {
            tvStudentName.setText("学生: " + (grade.getStudentName() != null ? grade.getStudentName() : "未知"));
            tvCourseName.setText("课程: " + (grade.getCourseName() != null ? grade.getCourseName() : "未知"));
            tvScore.setText(grade.getScore() + "分");
            tvExamType.setText("考试类型: " + grade.getExamType());
            tvExamDate.setText("考试日期: " + grade.getExamDate());
            tvSemester.setText("学期: " + grade.getSemester());

            // 根据权限控制按钮显示
            if (isStudentOnly) {
                btnEdit.setVisibility(View.GONE);
                btnDelete.setVisibility(View.GONE);
            } else {
                btnEdit.setVisibility(View.VISIBLE);
                btnDelete.setVisibility(View.VISIBLE);

                // 设置点击事件
                btnEdit.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (listener != null) {
                            listener.onGradeClick(grade);
                        }
                    }
                });

                btnDelete.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (listener != null) {
                            listener.onGradeDelete(grade);
                        }
                    }
                });
            }

            // 整个item点击事件
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.onGradeClick(grade);
                    }
                }
            });
        }
    }
}
