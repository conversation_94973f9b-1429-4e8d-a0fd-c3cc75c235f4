package com.example.studentgrademanager.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.model.Course;
import java.util.List;

/**
 * 课程列表适配器
 * 用于RecyclerView显示课程信息
 */
public class CourseAdapter extends RecyclerView.Adapter<CourseAdapter.CourseViewHolder> {

    private List<Course> courseList;
    private OnCourseClickListener listener;

    public interface OnCourseClickListener {
        void onCourseClick(Course course);
        void onCourseDelete(Course course);
    }

    public CourseAdapter(List<Course> courseList, OnCourseClickListener listener) {
        this.courseList = courseList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public CourseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_course, parent, false);
        return new CourseViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CourseViewHolder holder, int position) {
        Course course = courseList.get(position);
        holder.bind(course);
    }

    @Override
    public int getItemCount() {
        return courseList.size();
    }

    /**
     * 更新数据
     */
    public void updateData(List<Course> newCourseList) {
        this.courseList = newCourseList;
        notifyDataSetChanged();
    }

    class CourseViewHolder extends RecyclerView.ViewHolder {
        private TextView tvCourseId, tvCourseName, tvTeacher, tvCredits, tvSemester, tvDescription;
        private Button btnEdit, btnDelete;

        public CourseViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCourseId = itemView.findViewById(R.id.tv_course_id);
            tvCourseName = itemView.findViewById(R.id.tv_course_name);
            tvTeacher = itemView.findViewById(R.id.tv_course_teacher);
            tvCredits = itemView.findViewById(R.id.tv_course_credits);
            tvSemester = itemView.findViewById(R.id.tv_course_semester);
            tvDescription = itemView.findViewById(R.id.tv_course_description);
            btnEdit = itemView.findViewById(R.id.btn_edit_course);
            btnDelete = itemView.findViewById(R.id.btn_delete_course);
        }

        public void bind(Course course) {
            tvCourseId.setText("课程编号: " + course.getCourseId());
            tvCourseName.setText("课程名称: " + course.getCourseName());
            tvTeacher.setText("授课教师: " + course.getTeacher());
            tvCredits.setText("学分: " + course.getCredits());
            tvSemester.setText("学期: " + course.getSemester());
            tvDescription.setText("描述: " + (course.getDescription() != null ? course.getDescription() : "无"));

            // 设置点击事件
            btnEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.onCourseClick(course);
                    }
                }
            });

            btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.onCourseDelete(course);
                    }
                }
            });

            // 整个item点击事件
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.onCourseClick(course);
                    }
                }
            });
        }
    }
}
