package com.example.studentgrademanager.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.model.Student;
import java.util.List;

/**
 * 学生列表适配器
 * 用于RecyclerView显示学生信息
 */
public class StudentAdapter extends RecyclerView.Adapter<StudentAdapter.StudentViewHolder> {

    private List<Student> studentList;
    private OnStudentClickListener listener;
    private boolean isStudentOnly;

    public interface OnStudentClickListener {
        void onStudentClick(Student student);
        void onStudentDelete(Student student);
    }

    public StudentAdapter(List<Student> studentList, OnStudentClickListener listener) {
        this.studentList = studentList;
        this.listener = listener;
        this.isStudentOnly = false;
    }

    public StudentAdapter(List<Student> studentList, OnStudentClickListener listener, boolean isStudentOnly) {
        this.studentList = studentList;
        this.listener = listener;
        this.isStudentOnly = isStudentOnly;
    }

    @NonNull
    @Override
    public StudentViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_student, parent, false);
        return new StudentViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull StudentViewHolder holder, int position) {
        Student student = studentList.get(position);
        holder.bind(student);
    }

    @Override
    public int getItemCount() {
        return studentList.size();
    }

    /**
     * 更新数据
     */
    public void updateData(List<Student> newStudentList) {
        this.studentList = newStudentList;
        notifyDataSetChanged();
    }

    class StudentViewHolder extends RecyclerView.ViewHolder {
        private TextView tvStudentId, tvName, tvGender, tvAge, tvClass, tvPhone, tvEmail;
        private Button btnEdit, btnDelete;

        public StudentViewHolder(@NonNull View itemView) {
            super(itemView);
            tvStudentId = itemView.findViewById(R.id.tv_student_id);
            tvName = itemView.findViewById(R.id.tv_student_name);
            tvGender = itemView.findViewById(R.id.tv_student_gender);
            tvAge = itemView.findViewById(R.id.tv_student_age);
            tvClass = itemView.findViewById(R.id.tv_student_class);
            tvPhone = itemView.findViewById(R.id.tv_student_phone);
            tvEmail = itemView.findViewById(R.id.tv_student_email);
            btnEdit = itemView.findViewById(R.id.btn_edit_student);
            btnDelete = itemView.findViewById(R.id.btn_delete_student);
        }

        public void bind(Student student) {
            tvStudentId.setText("学号: " + student.getStudentId());
            tvName.setText("姓名: " + student.getName());
            tvGender.setText("性别: " + student.getGender());
            tvAge.setText("年龄: " + student.getAge());
            tvClass.setText("班级: " + student.getClassName());
            tvPhone.setText("电话: " + (student.getPhone() != null ? student.getPhone() : "未填写"));
            tvEmail.setText("邮箱: " + (student.getEmail() != null ? student.getEmail() : "未填写"));

            // 根据权限控制按钮显示
            if (isStudentOnly) {
                btnEdit.setVisibility(View.GONE);
                btnDelete.setVisibility(View.GONE);
            } else {
                btnEdit.setVisibility(View.VISIBLE);
                btnDelete.setVisibility(View.VISIBLE);

                // 设置点击事件
                btnEdit.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (listener != null) {
                            listener.onStudentClick(student);
                        }
                    }
                });

                btnDelete.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (listener != null) {
                            listener.onStudentDelete(student);
                        }
                    }
                });
            }

            // 整个item点击事件
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.onStudentClick(student);
                    }
                }
            });
        }
    }
}
