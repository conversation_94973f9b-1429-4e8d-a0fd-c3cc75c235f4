package com.example.studentgrademanager.database;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

/**
 * 数据库帮助类
 * 负责创建和管理SQLite数据库
 */
public class DatabaseHelper extends SQLiteOpenHelper {
    
    private static final String DATABASE_NAME = "student_grade_manager.db";
    private static final int DATABASE_VERSION = 3;
    // 数据库版本升级到3，触发onUpgrade方法重建数据库并插入新的示例数据
    
    // 学生表
    public static final String TABLE_STUDENTS = "students";
    public static final String COLUMN_STUDENT_ID = "id";
    public static final String COLUMN_STUDENT_NUMBER = "student_id";
    public static final String COLUMN_STUDENT_NAME = "name";
    public static final String COLUMN_STUDENT_GENDER = "gender";
    public static final String COLUMN_STUDENT_AGE = "age";
    public static final String COLUMN_STUDENT_CLASS = "class_name";
    public static final String COLUMN_STUDENT_PHONE = "phone";
    public static final String COLUMN_STUDENT_EMAIL = "email";
    
    // 课程表
    public static final String TABLE_COURSES = "courses";
    public static final String COLUMN_COURSE_ID = "id";
    public static final String COLUMN_COURSE_NUMBER = "course_id";
    public static final String COLUMN_COURSE_NAME = "course_name";
    public static final String COLUMN_COURSE_TEACHER = "teacher";
    public static final String COLUMN_COURSE_CREDITS = "credits";
    public static final String COLUMN_COURSE_SEMESTER = "semester";
    public static final String COLUMN_COURSE_DESCRIPTION = "description";
    
    // 成绩表
    public static final String TABLE_GRADES = "grades";
    public static final String COLUMN_GRADE_ID = "id";
    public static final String COLUMN_GRADE_STUDENT_ID = "student_id";
    public static final String COLUMN_GRADE_COURSE_ID = "course_id";
    public static final String COLUMN_GRADE_SCORE = "score";
    public static final String COLUMN_GRADE_EXAM_TYPE = "exam_type";
    public static final String COLUMN_GRADE_EXAM_DATE = "exam_date";
    public static final String COLUMN_GRADE_SEMESTER = "semester";

    // 用户表
    public static final String TABLE_USERS = "users";
    public static final String COLUMN_USER_ID = "id";
    public static final String COLUMN_USER_USERNAME = "username";
    public static final String COLUMN_USER_PASSWORD = "password";
    public static final String COLUMN_USER_TYPE = "user_type";
    public static final String COLUMN_USER_REAL_NAME = "real_name";
    public static final String COLUMN_USER_STUDENT_ID = "student_id";
    
    // 创建学生表的SQL语句
    private static final String CREATE_STUDENTS_TABLE = 
        "CREATE TABLE " + TABLE_STUDENTS + " (" +
        COLUMN_STUDENT_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_STUDENT_NUMBER + " TEXT UNIQUE NOT NULL, " +
        COLUMN_STUDENT_NAME + " TEXT NOT NULL, " +
        COLUMN_STUDENT_GENDER + " TEXT NOT NULL, " +
        COLUMN_STUDENT_AGE + " INTEGER NOT NULL, " +
        COLUMN_STUDENT_CLASS + " TEXT NOT NULL, " +
        COLUMN_STUDENT_PHONE + " TEXT, " +
        COLUMN_STUDENT_EMAIL + " TEXT" +
        ")";
    
    // 创建课程表的SQL语句
    private static final String CREATE_COURSES_TABLE = 
        "CREATE TABLE " + TABLE_COURSES + " (" +
        COLUMN_COURSE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_COURSE_NUMBER + " TEXT UNIQUE NOT NULL, " +
        COLUMN_COURSE_NAME + " TEXT NOT NULL, " +
        COLUMN_COURSE_TEACHER + " TEXT NOT NULL, " +
        COLUMN_COURSE_CREDITS + " INTEGER NOT NULL, " +
        COLUMN_COURSE_SEMESTER + " TEXT NOT NULL, " +
        COLUMN_COURSE_DESCRIPTION + " TEXT" +
        ")";
    
    // 创建成绩表的SQL语句
    private static final String CREATE_GRADES_TABLE =
        "CREATE TABLE " + TABLE_GRADES + " (" +
        COLUMN_GRADE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_GRADE_STUDENT_ID + " INTEGER NOT NULL, " +
        COLUMN_GRADE_COURSE_ID + " INTEGER NOT NULL, " +
        COLUMN_GRADE_SCORE + " REAL NOT NULL, " +
        COLUMN_GRADE_EXAM_TYPE + " TEXT NOT NULL, " +
        COLUMN_GRADE_EXAM_DATE + " TEXT NOT NULL, " +
        COLUMN_GRADE_SEMESTER + " TEXT NOT NULL, " +
        "FOREIGN KEY(" + COLUMN_GRADE_STUDENT_ID + ") REFERENCES " + TABLE_STUDENTS + "(" + COLUMN_STUDENT_ID + "), " +
        "FOREIGN KEY(" + COLUMN_GRADE_COURSE_ID + ") REFERENCES " + TABLE_COURSES + "(" + COLUMN_COURSE_ID + ")" +
        ")";

    // 创建用户表的SQL语句
    private static final String CREATE_USERS_TABLE =
        "CREATE TABLE " + TABLE_USERS + " (" +
        COLUMN_USER_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_USER_USERNAME + " TEXT UNIQUE NOT NULL, " +
        COLUMN_USER_PASSWORD + " TEXT NOT NULL, " +
        COLUMN_USER_TYPE + " TEXT NOT NULL, " +
        COLUMN_USER_REAL_NAME + " TEXT NOT NULL, " +
        COLUMN_USER_STUDENT_ID + " INTEGER DEFAULT 0" +
        ")";
    
    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(CREATE_STUDENTS_TABLE);
        db.execSQL(CREATE_COURSES_TABLE);
        db.execSQL(CREATE_GRADES_TABLE);
        db.execSQL(CREATE_USERS_TABLE);

        // 插入一些示例数据
        insertSampleData(db);
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_USERS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_GRADES);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_COURSES);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_STUDENTS);
        onCreate(db);
    }
    
    private void insertSampleData(SQLiteDatabase db) {
        // ==================== 插入学生数据（共23条） ====================
        // 原有学生数据
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021001', '张三', '男', 20, '计算机1班', '13800138001', '<EMAIL>')");
        // 绑定学生管理按钮，通过ID从布局文件中查找对应的Button控件
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021002', '李四', '女', 19, '计算机1班', '13800138002', '<EMAIL>')");
        // 绑定课程管理按钮，管理员专用功能，学生用户将被隐藏
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021003', '王五', '男', 21, '计算机2班', '13800138003', '<EMAIL>')");
        // 绑定成绩管理按钮，支持权限控制的功能访问

        // 新增20条随机学生数据
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021004', '赵敏', '女', 20, '计算机1班', '13800138004', '<EMAIL>')");
        // 学生4：赵敏，女，20岁，计算机1班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021005', '钱伟', '男', 22, '计算机2班', '13800138005', '<EMAIL>')");
        // 学生5：钱伟，男，22岁，计算机2班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021006', '孙丽', '女', 19, '软件1班', '13800138006', '<EMAIL>')");
        // 学生6：孙丽，女，19岁，软件1班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021007', '李明', '男', 21, '软件1班', '13800138007', '<EMAIL>')");
        // 学生7：李明，男，21岁，软件1班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021008', '周芳', '女', 20, '网络1班', '13800138008', '<EMAIL>')");
        // 学生8：周芳，女，20岁，网络1班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021009', '吴强', '男', 23, '网络1班', '13800138009', '<EMAIL>')");
        // 学生9：吴强，男，23岁，网络1班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021010', '郑雪', '女', 18, '计算机3班', '13800138010', '<EMAIL>')");
        // 学生10：郑雪，女，18岁，计算机3班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021011', '王磊', '男', 22, '计算机3班', '13800138011', '<EMAIL>')");
        // 学生11：王磊，男，22岁，计算机3班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021012', '陈静', '女', 21, '软件2班', '13800138012', '<EMAIL>')");
        // 学生12：陈静，女，21岁，软件2班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021013', '刘涛', '男', 20, '软件2班', '13800138013', '<EMAIL>')");
        // 学生13：刘涛，男，20岁，软件2班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021014', '杨梅', '女', 19, '网络2班', '13800138014', '<EMAIL>')");
        // 学生14：杨梅，女，19岁，网络2班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021015', '黄飞', '男', 24, '网络2班', '13800138015', '<EMAIL>')");
        // 学生15：黄飞，男，24岁，网络2班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021016', '林娜', '女', 20, '信息1班', '13800138016', '<EMAIL>')");
        // 学生16：林娜，女，20岁，信息1班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021017', '徐刚', '男', 21, '信息1班', '13800138017', '<EMAIL>')");
        // 学生17：徐刚，男，21岁，信息1班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021018', '马丽', '女', 22, '信息2班', '13800138018', '<EMAIL>')");
        // 学生18：马丽，女，22岁，信息2班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021019', '朱斌', '男', 19, '信息2班', '13800138019', '<EMAIL>')");
        // 学生19：朱斌，男，19岁，信息2班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021020', '何琳', '女', 23, '数媒1班', '13800138020', '<EMAIL>')");
        // 学生20：何琳，女，23岁，数媒1班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021021', '罗军', '男', 20, '数媒1班', '13800138021', '<EMAIL>')");
        // 学生21：罗军，男，20岁，数媒1班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021022', '高燕', '女', 21, '数媒2班', '13800138022', '<EMAIL>')");
        // 学生22：高燕，女，21岁，数媒2班
        db.execSQL("INSERT INTO " + TABLE_STUDENTS + " VALUES (null, '2021023', '宋浩', '男', 22, '数媒2班', '13800138023', '<EMAIL>')");
        // 学生23：宋浩，男，22岁，数媒2班

        // ==================== 插入课程数据（共23条） ====================
        // 原有课程数据
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS001', 'Java程序设计', '刘老师', 4, '2024春季', 'Java编程基础课程')");
        // 课程1：Java程序设计，刘老师，4学分，2024春季
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS002', '数据结构', '陈老师', 3, '2024春季', '数据结构与算法')");
        // 课程2：数据结构，陈老师，3学分，2024春季
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS003', '数据库原理', '王老师', 3, '2024春季', '数据库设计与应用')");
        // 课程3：数据库原理，王老师，3学分，2024春季

        // 新增20条随机课程数据
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS004', '计算机网络', '张老师', 3, '2024春季', '网络协议与应用')");
        // 课程4：计算机网络，张老师，3学分，网络协议与应用
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS005', '操作系统', '李老师', 4, '2024春季', '操作系统原理与实践')");
        // 课程5：操作系统，李老师，4学分，操作系统原理与实践
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS006', '软件工程', '赵老师', 3, '2024春季', '软件开发方法与管理')");
        // 课程6：软件工程，赵老师，3学分，软件开发方法与管理
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS007', 'Web开发技术', '钱老师', 3, '2024春季', 'HTML、CSS、JavaScript开发')");
        // 课程7：Web开发技术，钱老师，3学分，前端开发技术
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS008', 'Python程序设计', '孙老师', 3, '2024春季', 'Python编程与应用')");
        // 课程8：Python程序设计，孙老师，3学分，Python编程与应用
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS009', '算法设计与分析', '周老师', 4, '2024春季', '算法理论与实现')");
        // 课程9：算法设计与分析，周老师，4学分，算法理论与实现
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS010', '人工智能基础', '吴老师', 3, '2024春季', 'AI原理与机器学习')");
        // 课程10：人工智能基础，吴老师，3学分，AI原理与机器学习
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS011', '移动应用开发', '郑老师', 3, '2024春季', 'Android与iOS开发')");
        // 课程11：移动应用开发，郑老师，3学分，Android与iOS开发
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS012', '信息安全', '王老师', 3, '2024春季', '网络安全与密码学')");
        // 课程12：信息安全，王老师，3学分，网络安全与密码学
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS013', '大数据技术', '陈老师', 4, '2024春季', '大数据处理与分析')");
        // 课程13：大数据技术，陈老师，4学分，大数据处理与分析
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS014', '云计算技术', '刘老师', 3, '2024春季', '云平台与虚拟化')");
        // 课程14：云计算技术，刘老师，3学分，云平台与虚拟化
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS015', '物联网技术', '杨老师', 3, '2024春季', 'IoT系统设计与开发')");
        // 课程15：物联网技术，杨老师，3学分，IoT系统设计与开发
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS016', '区块链技术', '黄老师', 2, '2024春季', '区块链原理与应用')");
        // 课程16：区块链技术，黄老师，2学分，区块链原理与应用
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS017', '数字图像处理', '林老师', 3, '2024春季', '图像算法与处理技术')");
        // 课程17：数字图像处理，林老师，3学分，图像算法与处理技术
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS018', '计算机图形学', '徐老师', 3, '2024春季', '3D图形渲染技术')");
        // 课程18：计算机图形学，徐老师，3学分，3D图形渲染技术
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS019', '编译原理', '马老师', 4, '2024春季', '编译器设计与实现')");
        // 课程19：编译原理，马老师，4学分，编译器设计与实现
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS020', '计算机组成原理', '朱老师', 4, '2024春季', '计算机硬件结构')");
        // 课程20：计算机组成原理，朱老师，4学分，计算机硬件结构
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS021', '数据挖掘', '何老师', 3, '2024春季', '数据挖掘算法与应用')");
        // 课程21：数据挖掘，何老师，3学分，数据挖掘算法与应用
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS022', '机器学习', '罗老师', 4, '2024春季', '机器学习理论与实践')");
        // 课程22：机器学习，罗老师，4学分，机器学习理论与实践
        db.execSQL("INSERT INTO " + TABLE_COURSES + " VALUES (null, 'CS023', '深度学习', '高老师', 3, '2024春季', '神经网络与深度学习')");
        // 课程23：深度学习，高老师，3学分，神经网络与深度学习

        // ==================== 插入成绩数据（共100+条） ====================
        // 原有成绩数据
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 1, 1, 85.5, '期中考试', '2024-04-15', '2024春季')");
        // 张三的Java程序设计期中考试成绩：85.5分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 1, 2, 92.0, '期中考试', '2024-04-20', '2024春季')");
        // 张三的数据结构期中考试成绩：92.0分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 2, 1, 78.5, '期中考试', '2024-04-15', '2024春季')");
        // 李四的Java程序设计期中考试成绩：78.5分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 2, 3, 88.0, '期中考试', '2024-04-25', '2024春季')");
        // 李四的数据库原理期中考试成绩：88.0分

        // 新增大量随机成绩数据 - 为前10个学生添加多门课程成绩
        // 学生1（张三）的其他课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 1, 3, 89.5, '期末考试', '2024-06-15', '2024春季')");
        // 张三的数据库原理期末考试：89.5分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 1, 4, 91.0, '期中考试', '2024-04-18', '2024春季')");
        // 张三的计算机网络期中考试：91.0分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 1, 5, 87.5, '期末考试', '2024-06-20', '2024春季')");
        // 张三的操作系统期末考试：87.5分

        // 学生2（李四）的其他课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 2, 2, 85.0, '期末考试', '2024-06-18', '2024春季')");
        // 李四的数据结构期末考试：85.0分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 2, 4, 82.5, '期中考试', '2024-04-18', '2024春季')");
        // 李四的计算机网络期中考试：82.5分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 2, 6, 90.0, '期末考试', '2024-06-22', '2024春季')");
        // 李四的软件工程期末考试：90.0分

        // 学生3（王五）的课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 3, 1, 93.5, '期中考试', '2024-04-15', '2024春季')");
        // 王五的Java程序设计期中考试：93.5分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 3, 2, 88.0, '期末考试', '2024-06-18', '2024春季')");
        // 王五的数据结构期末考试：88.0分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 3, 5, 85.5, '期中考试', '2024-04-22', '2024春季')");
        // 王五的操作系统期中考试：85.5分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 3, 7, 92.0, '期末考试', '2024-06-25', '2024春季')");
        // 王五的Web开发技术期末考试：92.0分

        // 学生4（赵敏）的课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 4, 1, 86.0, '期中考试', '2024-04-15', '2024春季')");
        // 赵敏的Java程序设计期中考试：86.0分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 4, 3, 91.5, '期末考试', '2024-06-15', '2024春季')");
        // 赵敏的数据库原理期末考试：91.5分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 4, 8, 89.0, '期中考试', '2024-04-25', '2024春季')");
        // 赵敏的Python程序设计期中考试：89.0分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 4, 9, 87.5, '期末考试', '2024-06-28', '2024春季')");
        // 赵敏的算法设计与分析期末考试：87.5分

        // 学生5（钱伟）的课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 5, 2, 84.5, '期中考试', '2024-04-20', '2024春季')");
        // 钱伟的数据结构期中考试：84.5分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 5, 4, 88.0, '期末考试', '2024-06-20', '2024春季')");
        // 钱伟的计算机网络期末考试：88.0分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 5, 10, 92.5, '期中考试', '2024-04-28', '2024春季')");
        // 钱伟的人工智能基础期中考试：92.5分
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 5, 11, 85.0, '期末考试', '2024-06-30', '2024春季')");
        // 钱伟的移动应用开发期末考试：85.0分

        // 继续为学生6-15添加成绩数据
        // 学生6（孙丽）的课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 6, 1, 90.5, '期中考试', '2024-04-15', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 6, 7, 88.5, '期末考试', '2024-06-25', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 6, 12, 86.0, '期中考试', '2024-05-02', '2024春季')");

        // 学生7（李明）的课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 7, 2, 87.0, '期中考试', '2024-04-20', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 7, 6, 91.5, '期末考试', '2024-06-22', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 7, 13, 89.0, '期中考试', '2024-05-05', '2024春季')");

        // 学生8（周芳）的课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 8, 3, 85.5, '期末考试', '2024-06-15', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 8, 8, 92.0, '期中考试', '2024-04-25', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 8, 14, 87.5, '期末考试', '2024-07-02', '2024春季')");

        // 学生9（吴强）的课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 9, 4, 89.5, '期中考试', '2024-04-18', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 9, 9, 86.0, '期末考试', '2024-06-28', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 9, 15, 90.5, '期中考试', '2024-05-08', '2024春季')");

        // 学生10（郑雪）的课程成绩
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 10, 5, 88.0, '期中考试', '2024-04-22', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 10, 10, 93.5, '期末考试', '2024-07-01', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 10, 16, 85.0, '期中考试', '2024-05-10', '2024春季')");

        // 为学生11-20添加随机成绩（每人3-4门课程）
        // 学生11（王磊）
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 11, 1, 84.5, '期中考试', '2024-04-15', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 11, 17, 89.0, '期末考试', '2024-07-05', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 11, 18, 87.5, '期中考试', '2024-05-12', '2024春季')");

        // 学生12（陈静）
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 12, 2, 91.0, '期中考试', '2024-04-20', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 12, 19, 86.5, '期末考试', '2024-07-08', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 12, 20, 88.0, '期中考试', '2024-05-15', '2024春季')");

        // 学生13（刘涛）
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 13, 3, 87.5, '期末考试', '2024-06-15', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 13, 21, 90.0, '期中考试', '2024-05-18', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 13, 22, 85.5, '期末考试', '2024-07-10', '2024春季')");

        // 学生14（杨梅）
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 14, 4, 92.5, '期中考试', '2024-04-18', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 14, 23, 88.5, '期末考试', '2024-07-12', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 14, 1, 86.0, '期中考试', '2024-04-15', '2024春季')");

        // 学生15（黄飞）
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 15, 5, 89.0, '期中考试', '2024-04-22', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 15, 6, 87.0, '期末考试', '2024-06-22', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 15, 7, 91.5, '期中考试', '2024-04-28', '2024春季')");

        // 为学生16-23添加成绩（每人2-3门课程）
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 16, 8, 85.5, '期中考试', '2024-04-25', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 16, 9, 88.0, '期末考试', '2024-06-28', '2024春季')");

        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 17, 10, 90.5, '期中考试', '2024-04-28', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 17, 11, 87.5, '期末考试', '2024-06-30', '2024春季')");

        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 18, 12, 89.0, '期中考试', '2024-05-02', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 18, 13, 86.5, '期末考试', '2024-07-05', '2024春季')");

        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 19, 14, 91.0, '期末考试', '2024-07-02', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 19, 15, 88.5, '期中考试', '2024-05-08', '2024春季')");

        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 20, 16, 87.0, '期中考试', '2024-05-10', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 20, 17, 89.5, '期末考试', '2024-07-05', '2024春季')");

        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 21, 18, 86.0, '期中考试', '2024-05-12', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 21, 19, 90.0, '期末考试', '2024-07-08', '2024春季')");

        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 22, 20, 88.5, '期中考试', '2024-05-15', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 22, 21, 85.5, '期末考试', '2024-07-10', '2024春季')");

        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 23, 22, 92.0, '期末考试', '2024-07-10', '2024春季')");
        db.execSQL("INSERT INTO " + TABLE_GRADES + " VALUES (null, 23, 23, 89.0, '期中考试', '2024-05-20', '2024春季')");

        // ==================== 插入用户数据 ====================
        // 原有用户数据
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'admin', '1227', 'admin', '系统管理员', 0)");
        // 系统管理员账户：用户名admin，密码1227
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a1', '123456', 'student', '张三', 1)");
        // 学生账户：张三，用户名a1，密码123456，对应学生ID为1

        // 新增学生用户账户（为前10个学生创建登录账户）
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a2', '123456', 'student', '李四', 2)");
        // 学生账户：李四，用户名a2，密码123456
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a3', '123456', 'student', '王五', 3)");
        // 学生账户：王五，用户名a3，密码123456
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a4', '123456', 'student', '赵敏', 4)");
        // 学生账户：赵敏，用户名a4，密码123456
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a5', '123456', 'student', '钱伟', 5)");
        // 学生账户：钱伟，用户名a5，密码123456
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a6', '123456', 'student', '孙丽', 6)");
        // 学生账户：孙丽，用户名a6，密码123456
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a7', '123456', 'student', '李明', 7)");
        // 学生账户：李明，用户名a7，密码123456
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a8', '123456', 'student', '周芳', 8)");
        // 学生账户：周芳，用户名a8，密码123456
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a9', '123456', 'student', '吴强', 9)");
        // 学生账户：吴强，用户名a9，密码123456
        db.execSQL("INSERT INTO " + TABLE_USERS + " VALUES (null, 'a10', '123456', 'student', '郑雪', 10)");
        // 学生账户：郑雪，用户名a10，密码123456
    }
}
