package com.example.studentgrademanager.controller;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.SearchView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.adapter.StudentAdapter;
import com.example.studentgrademanager.dao.StudentDAO;
import com.example.studentgrademanager.model.Student;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.example.studentgrademanager.utils.SessionManager;
import com.example.studentgrademanager.utils.LogoutHelper;
import java.util.List;
import java.util.ArrayList;

/**
 * 学生管理Activity
 * 实现学生信息的浏览、搜索功能
 */
public class StudentManagementActivity extends AppCompatActivity implements StudentAdapter.OnStudentClickListener {

    private RecyclerView recyclerView;
    private StudentAdapter adapter;
    private SearchView searchView;
    private ExtendedFloatingActionButton fabAdd;

    private StudentDAO studentDAO;
    private SessionManager sessionManager;
    private List<Student> studentList;
    private boolean isStudentOnly = false;
    private int currentStudentId = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_student_management);

        initSessionManager();
        checkPermissions();
        initViews();
        initDatabase();
        loadStudents();
        setupSearchView();
    }

    /**
     * 初始化会话管理
     */
    private void initSessionManager() {
        sessionManager = new SessionManager(this);
    }

    /**
     * 检查权限
     */
    private void checkPermissions() {
        Intent intent = getIntent();
        isStudentOnly = intent.getBooleanExtra("student_only", false);
        currentStudentId = intent.getIntExtra("student_id", 0);

        if (isStudentOnly) {
            setTitle("个人信息");
        } else {
            setTitle("学生管理");
        }
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        recyclerView = findViewById(R.id.recycler_view_students);
        searchView = findViewById(R.id.search_view_students);
        fabAdd = findViewById(R.id.fab_add_student);

        // 设置RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(this));

        // 根据权限设置添加按钮
        if (isStudentOnly) {
            fabAdd.setVisibility(View.GONE);
        } else {
            // 设置添加按钮点击事件
            fabAdd.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(StudentManagementActivity.this, StudentEditActivity.class);
                    startActivityForResult(intent, 1001);
                }
            });
        }
    }

    /**
     * 初始化数据库
     */
    private void initDatabase() {
        studentDAO = new StudentDAO(this);
        studentDAO.open();
    }

    /**
     * 加载学生数据
     */
    private void loadStudents() {
        if (isStudentOnly && currentStudentId > 0) {
            // 学生用户只能查看自己的信息
            Student student = studentDAO.getStudentById(currentStudentId);
            studentList = new ArrayList<>();
            if (student != null) {
                studentList.add(student);
            }
        } else {
            // 管理员可以查看所有学生
            studentList = studentDAO.getAllStudents();
        }
        adapter = new StudentAdapter(studentList, this, isStudentOnly);
        recyclerView.setAdapter(adapter);
    }

    /**
     * 设置搜索功能
     */
    private void setupSearchView() {
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                searchStudents(query);
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                if (newText.isEmpty()) {
                    loadStudents();
                } else {
                    searchStudents(newText);
                }
                return true;
            }
        });
    }

    /**
     * 搜索学生
     */
    private void searchStudents(String keyword) {
        List<Student> searchResults = studentDAO.searchStudents(keyword);
        adapter.updateData(searchResults);
    }

    @Override
    public void onStudentClick(Student student) {
        if (isStudentOnly) {
            // 学生用户只能查看，不能编辑
            Toast.makeText(this, "学生用户无法编辑信息", Toast.LENGTH_SHORT).show();
        } else {
            // 管理员可以编辑
            Intent intent = new Intent(this, StudentEditActivity.class);
            intent.putExtra("student_id", student.getId());
            intent.putExtra("mode", "edit");
            startActivityForResult(intent, 1002);
        }
    }

    @Override
    public void onStudentDelete(Student student) {
        if (isStudentOnly) {
            // 学生用户无法删除
            Toast.makeText(this, "学生用户无法删除信息", Toast.LENGTH_SHORT).show();
        } else {
            // 管理员可以删除
            int result = studentDAO.deleteStudent(student.getId());
            if (result > 0) {
                Toast.makeText(this, "删除成功", Toast.LENGTH_SHORT).show();
                loadStudents(); // 重新加载数据
            } else {
                Toast.makeText(this, "删除失败", Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            // 刷新学生列表
            loadStudents();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_logout) {
            logout();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 用户登出
     */
    private void logout() {
        LogoutHelper.showLogoutDialog(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (studentDAO != null) {
            studentDAO.close();
        }
    }
}
