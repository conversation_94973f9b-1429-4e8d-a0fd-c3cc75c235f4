package com.example.studentgrademanager.controller;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.dao.StudentDAO;
import com.example.studentgrademanager.model.Student;
import com.example.studentgrademanager.utils.SessionManager;
import com.example.studentgrademanager.utils.LogoutHelper;
import android.content.Intent;

/**
 * 学生编辑Activity
 * 实现学生信息的添加和修改功能，包含输入验证
 */
public class StudentEditActivity extends AppCompatActivity {

    private EditText etStudentId, etName, etAge, etClassName, etPhone, etEmail;
    private Spinner spinnerGender;
    
    private StudentDAO studentDAO;
    private SessionManager sessionManager;
    private Student currentStudent;
    private boolean isEditMode = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_student_edit);

        sessionManager = new SessionManager(this);
        initViews();
        initDatabase();
        setupSpinners();
        loadStudentData();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        etStudentId = findViewById(R.id.et_student_id);
        etName = findViewById(R.id.et_student_name);
        etAge = findViewById(R.id.et_student_age);
        etClassName = findViewById(R.id.et_student_class);
        etPhone = findViewById(R.id.et_student_phone);
        etEmail = findViewById(R.id.et_student_email);
        spinnerGender = findViewById(R.id.spinner_gender);
    }

    /**
     * 初始化数据库
     */
    private void initDatabase() {
        studentDAO = new StudentDAO(this);
        studentDAO.open();
    }

    /**
     * 设置下拉列表
     */
    private void setupSpinners() {
        // 性别下拉列表
        String[] genders = {"男", "女"};
        ArrayAdapter<String> genderAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, genders);
        genderAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerGender.setAdapter(genderAdapter);
    }

    /**
     * 加载学生数据（编辑模式）
     */
    private void loadStudentData() {
        int studentId = getIntent().getIntExtra("student_id", -1);
        String mode = getIntent().getStringExtra("mode");
        
        if (studentId != -1 && "edit".equals(mode)) {
            isEditMode = true;
            setTitle("编辑学生信息");
            
            currentStudent = studentDAO.getStudentById(studentId);
            if (currentStudent != null) {
                etStudentId.setText(currentStudent.getStudentId());
                etName.setText(currentStudent.getName());
                etAge.setText(String.valueOf(currentStudent.getAge()));
                etClassName.setText(currentStudent.getClassName());
                etPhone.setText(currentStudent.getPhone());
                etEmail.setText(currentStudent.getEmail());
                
                // 设置性别选择
                String gender = currentStudent.getGender();
                if ("女".equals(gender)) {
                    spinnerGender.setSelection(1);
                } else {
                    spinnerGender.setSelection(0);
                }
            }
        } else {
            isEditMode = false;
            setTitle("添加学生");
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_edit, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_save) {
            saveStudent();
            return true;
        } else if (id == R.id.action_logout) {
            logout();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 保存学生信息
     */
    private void saveStudent() {
        if (!validateInput()) {
            return;
        }

        String studentId = etStudentId.getText().toString().trim();
        String name = etName.getText().toString().trim();
        String gender = spinnerGender.getSelectedItem().toString();
        int age = Integer.parseInt(etAge.getText().toString().trim());
        String className = etClassName.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();
        String email = etEmail.getText().toString().trim();

        if (isEditMode) {
            // 更新学生信息
            currentStudent.setStudentId(studentId);
            currentStudent.setName(name);
            currentStudent.setGender(gender);
            currentStudent.setAge(age);
            currentStudent.setClassName(className);
            currentStudent.setPhone(phone);
            currentStudent.setEmail(email);

            int result = studentDAO.updateStudent(currentStudent);
            if (result > 0) {
                Toast.makeText(this, "更新成功", Toast.LENGTH_SHORT).show();
                setResult(RESULT_OK);
                finish();
            } else {
                Toast.makeText(this, "更新失败", Toast.LENGTH_SHORT).show();
            }
        } else {
            // 添加新学生
            Student newStudent = new Student(studentId, name, gender, age, className, phone, email);
            long result = studentDAO.addStudent(newStudent);
            if (result != -1) {
                Toast.makeText(this, "添加成功", Toast.LENGTH_SHORT).show();
                setResult(RESULT_OK);
                finish();
            } else {
                Toast.makeText(this, "添加失败，学号可能已存在", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 输入验证
     */
    private boolean validateInput() {
        String studentId = etStudentId.getText().toString().trim();
        String name = etName.getText().toString().trim();
        String ageStr = etAge.getText().toString().trim();
        String className = etClassName.getText().toString().trim();
        String email = etEmail.getText().toString().trim();

        if (TextUtils.isEmpty(studentId)) {
            etStudentId.setError("请输入学号");
            etStudentId.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(name)) {
            etName.setError("请输入姓名");
            etName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(ageStr)) {
            etAge.setError("请输入年龄");
            etAge.requestFocus();
            return false;
        }

        try {
            int age = Integer.parseInt(ageStr);
            if (age < 16 || age > 30) {
                etAge.setError("年龄应在16-30之间");
                etAge.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            etAge.setError("请输入有效的年龄");
            etAge.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(className)) {
            etClassName.setError("请输入班级");
            etClassName.requestFocus();
            return false;
        }

        // 邮箱格式验证
        if (!TextUtils.isEmpty(email) && !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            etEmail.setError("请输入有效的邮箱地址");
            etEmail.requestFocus();
            return false;
        }

        // 检查学号是否已存在（添加模式）
        if (!isEditMode) {
            Student existingStudent = studentDAO.getStudentByNumber(studentId);
            if (existingStudent != null) {
                etStudentId.setError("该学号已存在");
                etStudentId.requestFocus();
                return false;
            }
        }

        return true;
    }

    /**
     * 用户登出
     */
    private void logout() {
        LogoutHelper.showLogoutDialog(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (studentDAO != null) {
            studentDAO.close();
        }
    }
}
