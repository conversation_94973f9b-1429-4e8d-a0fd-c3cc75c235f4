package com.example.studentgrademanager.controller;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.dao.CourseDAO;
import com.example.studentgrademanager.model.Course;
import com.example.studentgrademanager.utils.SessionManager;
import com.example.studentgrademanager.utils.LogoutHelper;
import android.content.Intent;

/**
 * 课程编辑Activity
 * 实现课程信息的添加和修改功能，包含输入验证
 */
public class CourseEditActivity extends AppCompatActivity {

    private EditText etCourseId, etCourseName, etTeacher, etCredits, etDescription;
    private Spinner spinnerSemester;
    
    private CourseDAO courseDAO;
    private SessionManager sessionManager;
    private Course currentCourse;
    private boolean isEditMode = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_course_edit);

        sessionManager = new SessionManager(this);
        initViews();
        initDatabase();
        setupSpinners();
        loadCourseData();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        etCourseId = findViewById(R.id.et_course_id);
        etCourseName = findViewById(R.id.et_course_name);
        etTeacher = findViewById(R.id.et_course_teacher);
        etCredits = findViewById(R.id.et_course_credits);
        etDescription = findViewById(R.id.et_course_description);
        spinnerSemester = findViewById(R.id.spinner_semester);
    }

    /**
     * 初始化数据库
     */
    private void initDatabase() {
        courseDAO = new CourseDAO(this);
        courseDAO.open();
    }

    /**
     * 设置下拉列表
     */
    private void setupSpinners() {
        // 学期下拉列表
        String[] semesters = {"2024春季", "2024秋季", "2025春季", "2025秋季"};
        ArrayAdapter<String> semesterAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, semesters);
        semesterAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerSemester.setAdapter(semesterAdapter);
    }

    /**
     * 加载课程数据（编辑模式）
     */
    private void loadCourseData() {
        int courseId = getIntent().getIntExtra("course_id", -1);
        String mode = getIntent().getStringExtra("mode");
        
        if (courseId != -1 && "edit".equals(mode)) {
            isEditMode = true;
            setTitle("编辑课程信息");
            
            currentCourse = courseDAO.getCourseById(courseId);
            if (currentCourse != null) {
                etCourseId.setText(currentCourse.getCourseId());
                etCourseName.setText(currentCourse.getCourseName());
                etTeacher.setText(currentCourse.getTeacher());
                etCredits.setText(String.valueOf(currentCourse.getCredits()));
                etDescription.setText(currentCourse.getDescription());
                
                // 设置学期选择
                String semester = currentCourse.getSemester();
                ArrayAdapter<String> adapter = (ArrayAdapter<String>) spinnerSemester.getAdapter();
                int position = adapter.getPosition(semester);
                if (position >= 0) {
                    spinnerSemester.setSelection(position);
                }
            }
        } else {
            isEditMode = false;
            setTitle("添加课程");
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_edit, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_save) {
            saveCourse();
            return true;
        } else if (id == R.id.action_logout) {
            logout();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 保存课程信息
     */
    private void saveCourse() {
        if (!validateInput()) {
            return;
        }

        String courseId = etCourseId.getText().toString().trim();
        String courseName = etCourseName.getText().toString().trim();
        String teacher = etTeacher.getText().toString().trim();
        int credits = Integer.parseInt(etCredits.getText().toString().trim());
        String semester = spinnerSemester.getSelectedItem().toString();
        String description = etDescription.getText().toString().trim();

        if (isEditMode) {
            // 更新课程信息
            currentCourse.setCourseId(courseId);
            currentCourse.setCourseName(courseName);
            currentCourse.setTeacher(teacher);
            currentCourse.setCredits(credits);
            currentCourse.setSemester(semester);
            currentCourse.setDescription(description);

            int result = courseDAO.updateCourse(currentCourse);
            if (result > 0) {
                Toast.makeText(this, "更新成功", Toast.LENGTH_SHORT).show();
                setResult(RESULT_OK);
                finish();
            } else {
                Toast.makeText(this, "更新失败", Toast.LENGTH_SHORT).show();
            }
        } else {
            // 添加新课程
            Course newCourse = new Course(courseId, courseName, teacher, credits, semester, description);
            long result = courseDAO.addCourse(newCourse);
            if (result != -1) {
                Toast.makeText(this, "添加成功", Toast.LENGTH_SHORT).show();
                setResult(RESULT_OK);
                finish();
            } else {
                Toast.makeText(this, "添加失败，课程编号可能已存在", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 输入验证
     */
    private boolean validateInput() {
        String courseId = etCourseId.getText().toString().trim();
        String courseName = etCourseName.getText().toString().trim();
        String teacher = etTeacher.getText().toString().trim();
        String creditsStr = etCredits.getText().toString().trim();

        if (TextUtils.isEmpty(courseId)) {
            etCourseId.setError("请输入课程编号");
            etCourseId.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(courseName)) {
            etCourseName.setError("请输入课程名称");
            etCourseName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(teacher)) {
            etTeacher.setError("请输入授课教师");
            etTeacher.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(creditsStr)) {
            etCredits.setError("请输入学分");
            etCredits.requestFocus();
            return false;
        }

        try {
            int credits = Integer.parseInt(creditsStr);
            if (credits < 1 || credits > 10) {
                etCredits.setError("学分应在1-10之间");
                etCredits.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            etCredits.setError("请输入有效的学分");
            etCredits.requestFocus();
            return false;
        }

        // 检查课程编号是否已存在（添加模式）
        if (!isEditMode) {
            Course existingCourse = courseDAO.getCourseByNumber(courseId);
            if (existingCourse != null) {
                etCourseId.setError("该课程编号已存在");
                etCourseId.requestFocus();
                return false;
            }
        }

        return true;
    }

    /**
     * 用户登出
     */
    private void logout() {
        LogoutHelper.showLogoutDialog(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (courseDAO != null) {
            courseDAO.close();
        }
    }
}
