package com.example.studentgrademanager.controller;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.SearchView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.adapter.GradeAdapter;
import com.example.studentgrademanager.dao.GradeDAO;
import com.example.studentgrademanager.model.Grade;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.example.studentgrademanager.utils.SessionManager;
import com.example.studentgrademanager.utils.LogoutHelper;
import java.util.List;
import java.util.ArrayList;

/**
 * 成绩管理Activity
 * 实现成绩信息的浏览、搜索功能
 */
public class GradeManagementActivity extends AppCompatActivity implements GradeAdapter.OnGradeClickListener {

    private RecyclerView recyclerView;
    private GradeAdapter adapter;
    private SearchView searchView;
    private ExtendedFloatingActionButton fabAdd;

    private GradeDAO gradeDAO;
    private SessionManager sessionManager;
    private List<Grade> gradeList;
    private boolean isStudentOnly = false;
    private int currentStudentId = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_grade_management);

        initSessionManager();
        checkPermissions();
        initViews();
        initDatabase();
        loadGrades();
        setupSearchView();
    }

    /**
     * 初始化会话管理
     */
    private void initSessionManager() {
        sessionManager = new SessionManager(this);
    }

    /**
     * 检查权限
     */
    private void checkPermissions() {
        Intent intent = getIntent();
        isStudentOnly = intent.getBooleanExtra("student_only", false);
        currentStudentId = intent.getIntExtra("student_id", 0);

        if (isStudentOnly) {
            setTitle("个人成绩");
        } else {
            setTitle("成绩管理");
        }
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        recyclerView = findViewById(R.id.recycler_view_grades);
        searchView = findViewById(R.id.search_view_grades);
        fabAdd = findViewById(R.id.fab_add_grade);

        // 设置RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(this));

        // 根据权限设置添加按钮
        if (isStudentOnly) {
            fabAdd.setVisibility(View.GONE);
        } else {
            // 设置添加按钮点击事件
            fabAdd.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(GradeManagementActivity.this, GradeEditActivity.class);
                    startActivityForResult(intent, 1001);
                }
            });
        }
    }

    /**
     * 初始化数据库
     */
    private void initDatabase() {
        gradeDAO = new GradeDAO(this);
        gradeDAO.open();
    }

    /**
     * 加载成绩数据
     */
    private void loadGrades() {
        if (isStudentOnly && currentStudentId > 0) {
            // 学生用户只能查看自己的成绩
            gradeList = gradeDAO.getGradesByStudentId(currentStudentId);
        } else {
            // 管理员可以查看所有成绩
            gradeList = gradeDAO.getAllGrades();
        }
        adapter = new GradeAdapter(gradeList, this, isStudentOnly);
        recyclerView.setAdapter(adapter);
    }

    /**
     * 设置搜索功能
     */
    private void setupSearchView() {
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                // 这里可以实现按学生姓名或课程名称搜索
                loadGrades(); // 暂时重新加载所有数据
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                if (newText.isEmpty()) {
                    loadGrades();
                }
                return true;
            }
        });
    }

    @Override
    public void onGradeClick(Grade grade) {
        if (isStudentOnly) {
            // 学生用户只能查看，不能编辑
            Toast.makeText(this, "学生用户无法编辑成绩", Toast.LENGTH_SHORT).show();
        } else {
            // 管理员可以编辑
            Intent intent = new Intent(this, GradeEditActivity.class);
            intent.putExtra("grade_id", grade.getId());
            intent.putExtra("mode", "edit");
            startActivityForResult(intent, 1002);
        }
    }

    @Override
    public void onGradeDelete(Grade grade) {
        if (isStudentOnly) {
            // 学生用户无法删除
            Toast.makeText(this, "学生用户无法删除成绩", Toast.LENGTH_SHORT).show();
        } else {
            // 管理员可以删除
            int result = gradeDAO.deleteGrade(grade.getId());
            if (result > 0) {
                Toast.makeText(this, "删除成功", Toast.LENGTH_SHORT).show();
                loadGrades(); // 重新加载数据
            } else {
                Toast.makeText(this, "删除失败", Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            // 刷新成绩列表
            loadGrades();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_logout) {
            logout();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 用户登出
     */
    private void logout() {
        LogoutHelper.showLogoutDialog(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (gradeDAO != null) {
            gradeDAO.close();
        }
    }
}
