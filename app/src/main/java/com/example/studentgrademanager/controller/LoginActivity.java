package com.example.studentgrademanager.controller;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.example.studentgrademanager.MainActivity;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.dao.UserDAO;
import com.example.studentgrademanager.model.User;
import com.example.studentgrademanager.utils.SessionManager;
import com.example.studentgrademanager.utils.WelcomeToast;
import com.google.android.material.button.MaterialButton;

/**
 * 登录Activity
 * 实现用户登录验证和权限管理
 */
public class LoginActivity extends AppCompatActivity {

    private EditText etUsername, etPassword;
    private MaterialButton btnLogin;
    
    private UserDAO userDAO;
    private SessionManager sessionManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        initViews();
        initDatabase();
        checkLoginStatus();
        setupClickListeners();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        etUsername = findViewById(R.id.et_username);
        etPassword = findViewById(R.id.et_password);
        btnLogin = findViewById(R.id.btn_login);
    }

    /**
     * 初始化数据库和会话管理
     */
    private void initDatabase() {
        userDAO = new UserDAO(this);
        userDAO.open();
        sessionManager = new SessionManager(this);
    }

    /**
     * 检查登录状态
     */
    private void checkLoginStatus() {
        if (sessionManager.isLoggedIn()) {
            // 已登录，直接跳转到主界面
            navigateToMain();
        }
    }

    /**
     * 设置点击事件监听器
     */
    private void setupClickListeners() {
        btnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                attemptLogin();
            }
        });
    }

    /**
     * 尝试登录
     */
    private void attemptLogin() {
        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();

        // 输入验证
        if (!validateInput(username, password)) {
            return;
        }

        // 显示登录动画
        showLoginAnimation();

        // 验证用户凭据
        User user = userDAO.login(username, password);
        if (user != null) {
            // 登录成功
            sessionManager.createLoginSession(user);
            showWelcomeAnimation(user);
        } else {
            // 登录失败
            hideLoginAnimation();
            showErrorMessage("用户名或密码错误");
        }
    }

    /**
     * 输入验证
     */
    private boolean validateInput(String username, String password) {
        if (TextUtils.isEmpty(username)) {
            etUsername.setError("请输入用户名");
            etUsername.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(password)) {
            etPassword.setError("请输入密码");
            etPassword.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * 显示登录动画
     */
    private void showLoginAnimation() {
        btnLogin.setEnabled(false);
        btnLogin.setText("登录中...");
        
        // 按钮缩放动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(btnLogin, "scaleX", 1.0f, 0.95f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(btnLogin, "scaleY", 1.0f, 0.95f, 1.0f);
        scaleX.setDuration(300);
        scaleY.setDuration(300);
        scaleX.setInterpolator(new AccelerateDecelerateInterpolator());
        scaleY.setInterpolator(new AccelerateDecelerateInterpolator());
        scaleX.start();
        scaleY.start();
    }

    /**
     * 隐藏登录动画
     */
    private void hideLoginAnimation() {
        btnLogin.setEnabled(true);
        btnLogin.setText("登录");
    }

    /**
     * 显示欢迎动画
     */
    private void showWelcomeAnimation(User user) {
        // 创建欢迎消息
        String welcomeMessage = "登录成功，欢迎 " + user.getRealName();

        // 显示自定义欢迎Toast
        WelcomeToast welcomeToast = new WelcomeToast(this);
        welcomeToast.show(welcomeMessage);

        // 延迟跳转，让用户看到欢迎消息
        btnLogin.postDelayed(new Runnable() {
            @Override
            public void run() {
                navigateToMain();
            }
        }, 2500);
    }

    /**
     * 显示错误消息
     */
    private void showErrorMessage(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        
        // 输入框震动效果
        ObjectAnimator shakeX = ObjectAnimator.ofFloat(etUsername, "translationX", 0, 25, -25, 25, -25, 15, -15, 6, -6, 0);
        shakeX.setDuration(500);
        shakeX.start();
    }

    /**
     * 跳转到主界面
     */
    private void navigateToMain() {
        Intent intent = new Intent(LoginActivity.this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
        
        // 添加过渡动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (userDAO != null) {
            userDAO.close();
        }
    }
}
