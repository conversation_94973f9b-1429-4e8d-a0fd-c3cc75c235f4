package com.example.studentgrademanager.controller;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.dao.GradeDAO;
import com.example.studentgrademanager.dao.StudentDAO;
import com.example.studentgrademanager.dao.CourseDAO;
import com.example.studentgrademanager.model.Grade;
import com.example.studentgrademanager.model.Student;
import com.example.studentgrademanager.model.Course;
import com.example.studentgrademanager.utils.SessionManager;
import com.example.studentgrademanager.utils.LogoutHelper;
import android.content.Intent;
import java.util.List;
import java.util.ArrayList;

/**
 * 成绩编辑Activity
 * 实现成绩信息的添加和修改功能，包含输入验证和多表联动
 */
public class GradeEditActivity extends AppCompatActivity {

    private Spinner spinnerStudent, spinnerCourse, spinnerExamType, spinnerSemester;
    private EditText etScore, etExamDate;
    
    private GradeDAO gradeDAO;
    private StudentDAO studentDAO;
    private CourseDAO courseDAO;
    private SessionManager sessionManager;
    private Grade currentGrade;
    private boolean isEditMode = false;
    
    private List<Student> studentList;
    private List<Course> courseList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_grade_edit);

        sessionManager = new SessionManager(this);
        initViews();
        initDatabase();
        loadData();
        setupSpinners();
        loadGradeData();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        spinnerStudent = findViewById(R.id.spinner_student);
        spinnerCourse = findViewById(R.id.spinner_course);
        spinnerExamType = findViewById(R.id.spinner_exam_type);
        spinnerSemester = findViewById(R.id.spinner_semester);
        etScore = findViewById(R.id.et_score);
        etExamDate = findViewById(R.id.et_exam_date);
    }

    /**
     * 初始化数据库
     */
    private void initDatabase() {
        gradeDAO = new GradeDAO(this);
        studentDAO = new StudentDAO(this);
        courseDAO = new CourseDAO(this);
        
        gradeDAO.open();
        studentDAO.open();
        courseDAO.open();
    }

    /**
     * 加载基础数据
     */
    private void loadData() {
        studentList = studentDAO.getAllStudents();
        courseList = courseDAO.getAllCourses();
    }

    /**
     * 设置下拉列表
     */
    private void setupSpinners() {
        // 学生下拉列表
        List<String> studentNames = new ArrayList<>();
        for (Student student : studentList) {
            studentNames.add(student.getName() + " (" + student.getStudentId() + ")");
        }
        ArrayAdapter<String> studentAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, studentNames);
        studentAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerStudent.setAdapter(studentAdapter);

        // 课程下拉列表
        List<String> courseNames = new ArrayList<>();
        for (Course course : courseList) {
            courseNames.add(course.getCourseName() + " (" + course.getCourseId() + ")");
        }
        ArrayAdapter<String> courseAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, courseNames);
        courseAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCourse.setAdapter(courseAdapter);

        // 考试类型下拉列表
        String[] examTypes = {"期中考试", "期末考试", "平时测验", "实验考核", "课程设计"};
        ArrayAdapter<String> examTypeAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, examTypes);
        examTypeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerExamType.setAdapter(examTypeAdapter);

        // 学期下拉列表
        String[] semesters = {"2024春季", "2024秋季", "2025春季", "2025秋季"};
        ArrayAdapter<String> semesterAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, semesters);
        semesterAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerSemester.setAdapter(semesterAdapter);
    }

    /**
     * 加载成绩数据（编辑模式）
     */
    private void loadGradeData() {
        int gradeId = getIntent().getIntExtra("grade_id", -1);
        String mode = getIntent().getStringExtra("mode");
        
        if (gradeId != -1 && "edit".equals(mode)) {
            isEditMode = true;
            setTitle("编辑成绩信息");
            
            currentGrade = gradeDAO.getGradeById(gradeId);
            if (currentGrade != null) {
                // 设置学生选择
                for (int i = 0; i < studentList.size(); i++) {
                    if (studentList.get(i).getId() == currentGrade.getStudentId()) {
                        spinnerStudent.setSelection(i);
                        break;
                    }
                }
                
                // 设置课程选择
                for (int i = 0; i < courseList.size(); i++) {
                    if (courseList.get(i).getId() == currentGrade.getCourseId()) {
                        spinnerCourse.setSelection(i);
                        break;
                    }
                }
                
                etScore.setText(String.valueOf(currentGrade.getScore()));
                etExamDate.setText(currentGrade.getExamDate());
                
                // 设置考试类型选择
                ArrayAdapter<String> examTypeAdapter = (ArrayAdapter<String>) spinnerExamType.getAdapter();
                int examTypePosition = examTypeAdapter.getPosition(currentGrade.getExamType());
                if (examTypePosition >= 0) {
                    spinnerExamType.setSelection(examTypePosition);
                }
                
                // 设置学期选择
                ArrayAdapter<String> semesterAdapter = (ArrayAdapter<String>) spinnerSemester.getAdapter();
                int semesterPosition = semesterAdapter.getPosition(currentGrade.getSemester());
                if (semesterPosition >= 0) {
                    spinnerSemester.setSelection(semesterPosition);
                }
            }
        } else {
            isEditMode = false;
            setTitle("添加成绩");
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_edit, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_save) {
            saveGrade();
            return true;
        } else if (id == R.id.action_logout) {
            logout();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 保存成绩信息
     */
    private void saveGrade() {
        if (!validateInput()) {
            return;
        }

        int studentPosition = spinnerStudent.getSelectedItemPosition();
        int coursePosition = spinnerCourse.getSelectedItemPosition();
        int studentId = studentList.get(studentPosition).getId();
        int courseId = courseList.get(coursePosition).getId();
        double score = Double.parseDouble(etScore.getText().toString().trim());
        String examType = spinnerExamType.getSelectedItem().toString();
        String examDate = etExamDate.getText().toString().trim();
        String semester = spinnerSemester.getSelectedItem().toString();

        if (isEditMode) {
            // 更新成绩信息
            currentGrade.setStudentId(studentId);
            currentGrade.setCourseId(courseId);
            currentGrade.setScore(score);
            currentGrade.setExamType(examType);
            currentGrade.setExamDate(examDate);
            currentGrade.setSemester(semester);

            int result = gradeDAO.updateGrade(currentGrade);
            if (result > 0) {
                Toast.makeText(this, "更新成功", Toast.LENGTH_SHORT).show();
                setResult(RESULT_OK);
                finish();
            } else {
                Toast.makeText(this, "更新失败", Toast.LENGTH_SHORT).show();
            }
        } else {
            // 添加新成绩
            Grade newGrade = new Grade(studentId, courseId, score, examType, examDate, semester);
            long result = gradeDAO.addGrade(newGrade);
            if (result != -1) {
                Toast.makeText(this, "添加成功", Toast.LENGTH_SHORT).show();
                setResult(RESULT_OK);
                finish();
            } else {
                Toast.makeText(this, "添加失败", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 输入验证
     */
    private boolean validateInput() {
        String scoreStr = etScore.getText().toString().trim();
        String examDate = etExamDate.getText().toString().trim();

        if (TextUtils.isEmpty(scoreStr)) {
            etScore.setError("请输入成绩");
            etScore.requestFocus();
            return false;
        }

        try {
            double score = Double.parseDouble(scoreStr);
            if (score < 0 || score > 100) {
                etScore.setError("成绩应在0-100之间");
                etScore.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            etScore.setError("请输入有效的成绩");
            etScore.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(examDate)) {
            etExamDate.setError("请输入考试日期");
            etExamDate.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * 用户登出
     */
    private void logout() {
        LogoutHelper.showLogoutDialog(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (gradeDAO != null) gradeDAO.close();
        if (studentDAO != null) studentDAO.close();
        if (courseDAO != null) courseDAO.close();
    }
}
