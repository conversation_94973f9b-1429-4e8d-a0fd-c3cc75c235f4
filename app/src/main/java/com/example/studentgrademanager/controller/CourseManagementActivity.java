package com.example.studentgrademanager.controller;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.SearchView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.adapter.CourseAdapter;
import com.example.studentgrademanager.dao.CourseDAO;
import com.example.studentgrademanager.model.Course;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.example.studentgrademanager.utils.SessionManager;
import com.example.studentgrademanager.utils.LogoutHelper;
import java.util.List;

/**
 * 课程管理Activity
 * 实现课程信息的浏览、搜索功能
 */
public class CourseManagementActivity extends AppCompatActivity implements CourseAdapter.OnCourseClickListener {

    private RecyclerView recyclerView;
    private CourseAdapter adapter;
    private SearchView searchView;
    private ExtendedFloatingActionButton fabAdd;
    
    private CourseDAO courseDAO;
    private SessionManager sessionManager;
    private List<Course> courseList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_course_management);

        sessionManager = new SessionManager(this);
        initViews();
        initDatabase();
        loadCourses();
        setupSearchView();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        recyclerView = findViewById(R.id.recycler_view_courses);
        searchView = findViewById(R.id.search_view_courses);
        fabAdd = findViewById(R.id.fab_add_course);

        // 设置RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        
        // 设置添加按钮点击事件
        fabAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(CourseManagementActivity.this, CourseEditActivity.class);
                startActivityForResult(intent, 1001);
            }
        });
    }

    /**
     * 初始化数据库
     */
    private void initDatabase() {
        courseDAO = new CourseDAO(this);
        courseDAO.open();
    }

    /**
     * 加载课程数据
     */
    private void loadCourses() {
        courseList = courseDAO.getAllCourses();
        adapter = new CourseAdapter(courseList, this);
        recyclerView.setAdapter(adapter);
    }

    /**
     * 设置搜索功能
     */
    private void setupSearchView() {
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                searchCourses(query);
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                if (newText.isEmpty()) {
                    loadCourses();
                } else {
                    searchCourses(newText);
                }
                return true;
            }
        });
    }

    /**
     * 搜索课程
     */
    private void searchCourses(String keyword) {
        List<Course> searchResults = courseDAO.searchCourses(keyword);
        adapter.updateData(searchResults);
    }

    @Override
    public void onCourseClick(Course course) {
        // 点击课程项，进入详情/编辑页面
        Intent intent = new Intent(this, CourseEditActivity.class);
        intent.putExtra("course_id", course.getId());
        intent.putExtra("mode", "edit");
        startActivityForResult(intent, 1002);
    }

    @Override
    public void onCourseDelete(Course course) {
        // 删除课程
        int result = courseDAO.deleteCourse(course.getId());
        if (result > 0) {
            Toast.makeText(this, "删除成功", Toast.LENGTH_SHORT).show();
            loadCourses(); // 重新加载数据
        } else {
            Toast.makeText(this, "删除失败", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            // 刷新课程列表
            loadCourses();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_logout) {
            logout();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 用户登出
     */
    private void logout() {
        LogoutHelper.showLogoutDialog(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (courseDAO != null) {
            courseDAO.close();
        }
    }
}
