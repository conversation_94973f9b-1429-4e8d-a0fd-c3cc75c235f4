package com.example.studentgrademanager.controller;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.studentgrademanager.R;
import com.example.studentgrademanager.adapter.StatisticsAdapter;
import com.example.studentgrademanager.dao.GradeDAO;
import com.example.studentgrademanager.dao.StudentDAO;
import com.example.studentgrademanager.dao.CourseDAO;
import com.example.studentgrademanager.model.Grade;
import com.example.studentgrademanager.utils.SessionManager;
import com.example.studentgrademanager.utils.LogoutHelper;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.github.mikephil.charting.utils.ColorTemplate;
import java.util.ArrayList;
import java.util.List;

/**
 * 统计分析Activity
 * 显示成绩统计图表和数据分析
 */
public class StatisticsActivity extends AppCompatActivity {

    private BarChart barChart;
    private PieChart pieChart;
    private RecyclerView recyclerView;
    private StatisticsAdapter adapter;
    
    private GradeDAO gradeDAO;
    private StudentDAO studentDAO;
    private CourseDAO courseDAO;
    private SessionManager sessionManager;
    private boolean isStudentOnly = false;
    private int currentStudentId = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_statistics);

        initSessionManager();
        checkPermissions();
        initViews();
        initDatabase();
        loadStatistics();
    }

    /**
     * 初始化会话管理
     */
    private void initSessionManager() {
        sessionManager = new SessionManager(this);
    }

    /**
     * 检查权限
     */
    private void checkPermissions() {
        Intent intent = getIntent();
        isStudentOnly = intent.getBooleanExtra("student_only", false);
        currentStudentId = intent.getIntExtra("student_id", 0);

        if (isStudentOnly) {
            setTitle("个人统计");
        } else {
            setTitle("成绩统计分析");
        }
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        barChart = findViewById(R.id.bar_chart);
        pieChart = findViewById(R.id.pie_chart);
        recyclerView = findViewById(R.id.recycler_view_statistics);

        recyclerView.setLayoutManager(new LinearLayoutManager(this));
    }

    /**
     * 初始化数据库
     */
    private void initDatabase() {
        gradeDAO = new GradeDAO(this);
        studentDAO = new StudentDAO(this);
        courseDAO = new CourseDAO(this);
        
        gradeDAO.open();
        studentDAO.open();
        courseDAO.open();
    }

    /**
     * 加载统计数据
     */
    private void loadStatistics() {
        setupBarChart();
        setupPieChart();
        setupStatisticsList();
    }

    /**
     * 设置柱状图 - 学生平均分排名
     */
    private void setupBarChart() {
        List<Grade> statistics = gradeDAO.getGradeStatistics();
        List<BarEntry> entries = new ArrayList<>();
        
        for (int i = 0; i < Math.min(statistics.size(), 10); i++) {
            Grade stat = statistics.get(i);
            entries.add(new BarEntry(i, (float) stat.getScore()));
        }

        BarDataSet dataSet = new BarDataSet(entries, "学生平均分");
        dataSet.setColors(ColorTemplate.MATERIAL_COLORS);
        dataSet.setValueTextSize(12f);

        BarData barData = new BarData(dataSet);
        barChart.setData(barData);
        barChart.getDescription().setText("前10名学生平均分排名");
        barChart.animateY(1000);
        barChart.invalidate();
    }

    /**
     * 设置饼图 - 成绩分布
     */
    private void setupPieChart() {
        List<Grade> allGrades;
        if (isStudentOnly && currentStudentId > 0) {
            allGrades = gradeDAO.getGradesByStudentId(currentStudentId);
        } else {
            allGrades = gradeDAO.getAllGrades();
        }
        
        int excellent = 0; // 90-100
        int good = 0;      // 80-89
        int average = 0;   // 70-79
        int poor = 0;      // 60-69
        int fail = 0;      // <60

        for (Grade grade : allGrades) {
            double score = grade.getScore();
            if (score >= 90) excellent++;
            else if (score >= 80) good++;
            else if (score >= 70) average++;
            else if (score >= 60) poor++;
            else fail++;
        }

        List<PieEntry> entries = new ArrayList<>();
        if (excellent > 0) entries.add(new PieEntry(excellent, "优秀(90-100)"));
        if (good > 0) entries.add(new PieEntry(good, "良好(80-89)"));
        if (average > 0) entries.add(new PieEntry(average, "中等(70-79)"));
        if (poor > 0) entries.add(new PieEntry(poor, "及格(60-69)"));
        if (fail > 0) entries.add(new PieEntry(fail, "不及格(<60)"));

        PieDataSet dataSet = new PieDataSet(entries, "成绩分布");
        dataSet.setColors(ColorTemplate.COLORFUL_COLORS);
        dataSet.setValueTextSize(12f);
        dataSet.setValueTextColor(Color.WHITE);

        PieData pieData = new PieData(dataSet);
        pieChart.setData(pieData);
        pieChart.getDescription().setText("成绩分布统计");
        pieChart.animateY(1000);
        pieChart.invalidate();
    }

    /**
     * 设置统计列表
     */
    private void setupStatisticsList() {
        List<Grade> statistics = gradeDAO.getGradeStatistics();
        adapter = new StatisticsAdapter(statistics);
        recyclerView.setAdapter(adapter);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_logout) {
            logout();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 用户登出
     */
    private void logout() {
        LogoutHelper.showLogoutDialog(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (gradeDAO != null) gradeDAO.close();
        if (studentDAO != null) studentDAO.close();
        if (courseDAO != null) courseDAO.close();
    }
}
