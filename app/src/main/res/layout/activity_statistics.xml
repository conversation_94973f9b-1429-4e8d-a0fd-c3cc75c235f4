<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/main_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 标题卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardElevation="8dp"
            app:cardCornerRadius="20dp"
            app:cardBackgroundColor="@android:color/transparent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/title_background"
                android:padding="20dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_statistics"
                    android:layout_marginEnd="12dp"
                    app:tint="@android:color/white" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="成绩统计分析"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 柱状图卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardElevation="8dp"
            app:cardCornerRadius="20dp"
            app:cardBackgroundColor="@color/background_white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="学生平均分排名"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="8dp" />

                <com.github.mikephil.charting.charts.BarChart
                    android:id="@+id/bar_chart"
                    android:layout_width="match_parent"
                    android:layout_height="250dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 饼图卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="成绩分布统计"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="8dp" />

                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/pie_chart"
                    android:layout_width="match_parent"
                    android:layout_height="250dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 详细统计列表 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardElevation="4dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="学生成绩排名详情"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="8dp" />

                <!-- 表头 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="@color/background_gray"
                    android:padding="8dp"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="排名"
                        android:textStyle="bold"
                        android:textColor="@color/primary_text"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="学生姓名"
                        android:textStyle="bold"
                        android:textColor="@color/primary_text"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:text="平均分"
                        android:textStyle="bold"
                        android:textColor="@color/primary_text"
                        android:gravity="center" />

                </LinearLayout>

                <!-- 统计列表 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view_statistics"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
