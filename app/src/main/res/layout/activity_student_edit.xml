<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="20dp"
    android:background="@drawable/main_background">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardElevation="8dp"
        app:cardCornerRadius="20dp"
        app:cardBackgroundColor="@color/background_white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="学生信息"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/primary_color"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- 学号输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:hint="学号"
            app:startIconDrawable="@drawable/ic_student_id"
            app:startIconTint="@color/button_primary"
            app:boxStrokeColor="@color/button_primary"
            app:hintTextColor="@color/button_primary"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_student_id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLength="20" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 姓名输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:hint="姓名"
            app:startIconDrawable="@drawable/ic_person"
            app:startIconTint="@color/button_primary"
            app:boxStrokeColor="@color/button_primary"
            app:hintTextColor="@color/button_primary"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_student_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPersonName"
                android:maxLength="50" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 性别选择 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="性别"
            app:startIconDrawable="@drawable/ic_gender"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <Spinner
                android:id="@+id/spinner_gender"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 年龄输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="年龄"
            app:startIconDrawable="@drawable/ic_age"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_student_age"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:maxLength="2" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 班级输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="班级"
            app:startIconDrawable="@drawable/ic_class"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_student_class"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLength="50" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 电话输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="电话（可选）"
            app:startIconDrawable="@drawable/ic_phone"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_student_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="phone"
                android:maxLength="20" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 邮箱输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:hint="邮箱（可选）"
            app:startIconDrawable="@drawable/ic_email"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_student_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textEmailAddress"
                android:maxLength="100" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 提示信息 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/info_background"
            app:cardElevation="2dp"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_info"
                    android:layout_marginEnd="8dp"
                    app:tint="@color/info_text" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="请填写完整的学生信息，带*号的为必填项。点击右上角保存按钮完成操作。"
                    android:textSize="12sp"
                    android:textColor="@color/info_text" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</ScrollView>
