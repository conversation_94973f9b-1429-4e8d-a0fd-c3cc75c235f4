<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp"
    app:cardElevation="8dp"
    app:cardCornerRadius="20dp"
    app:cardBackgroundColor="@color/background_card"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 课程基本信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <!-- 课程图标 -->
            <ImageView
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:src="@drawable/ic_course"
                android:background="@drawable/circle_background"
                android:padding="12dp"
                android:layout_marginEnd="16dp"
                app:tint="@color/button_secondary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_course_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="课程名称: Java程序设计"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text" />

                <TextView
                    android:id="@+id/tv_course_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="课程编号: CS001"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 详细信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/tv_course_teacher"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="教师: 刘老师"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text" />

                <TextView
                    android:id="@+id/tv_course_credits"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="学分: 4"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_course_semester"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="学期: 2024春季"
                android:textSize="14sp"
                android:textColor="@color/secondary_text"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tv_course_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="描述: Java编程基础课程"
                android:textSize="14sp"
                android:textColor="@color/secondary_text"
                android:maxLines="2"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_course"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="编辑"
                android:textSize="12sp"
                android:layout_marginEnd="8dp"
                app:icon="@drawable/ic_edit"
                app:iconSize="16dp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:backgroundTint="@color/button_outline" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete_course"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="删除"
                android:textSize="12sp"
                app:icon="@drawable/ic_delete"
                app:iconSize="16dp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:backgroundTint="@color/button_danger" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
