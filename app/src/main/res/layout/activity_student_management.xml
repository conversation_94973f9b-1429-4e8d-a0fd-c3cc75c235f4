<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/main_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 搜索栏 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            app:cardElevation="8dp"
            app:cardCornerRadius="16dp"
            app:cardBackgroundColor="@color/background_white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="4dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_student"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="8dp"
                    app:tint="@color/button_primary" />

                <SearchView
                    android:id="@+id/search_view_students"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:queryHint="搜索学生（姓名或学号）"
                    android:iconifiedByDefault="false"
                    android:background="@android:color/transparent" >

                </SearchView>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 学生列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_students"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="8dp"
            android:clipToPadding="false" />

        <!-- 空状态提示 -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@drawable/ic_empty_state"
                android:alpha="0.5"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="暂无学生数据"
                android:textSize="16sp"
                android:textColor="@color/secondary_text" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="点击右下角按钮添加学生"
                android:textSize="14sp"
                android:textColor="@color/secondary_text"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 添加学生按钮 -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fab_add_student"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="20dp"
        android:text="添加学生"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        app:icon="@drawable/ic_add"
        app:iconTint="@android:color/white"
        app:backgroundTint="@color/button_primary"
        app:elevation="12dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <Button
            android:id="@+id/button"
            android:layout_width="154dp"
            android:layout_height="107dp"
            android:text="Button" />

        <Button
            android:id="@+id/button2"
            android:layout_width="158dp"
            android:layout_height="109dp"
            android:text="Button" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <Button
            android:id="@+id/button3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Button" />

        <Button
            android:id="@+id/button4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Button" />
    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
