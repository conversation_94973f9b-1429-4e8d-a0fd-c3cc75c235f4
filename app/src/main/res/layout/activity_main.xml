<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="20dp"
    android:background="@drawable/main_background">

    <!-- 标题卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        app:cardElevation="8dp"
        app:cardCornerRadius="20dp"
        app:cardBackgroundColor="@android:color/transparent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/title_background"
            android:padding="24dp">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/ic_statistics"
                android:layout_gravity="center"
                android:layout_marginBottom="12dp"
                app:tint="@android:color/white" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="学生成绩统计管理系统"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:gravity="center" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Student Grade Management System"
                android:textSize="12sp"
                android:textColor="@color/accent_light"
                android:gravity="center"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 功能按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:paddingHorizontal="120dp">

        <!-- 学生管理按钮 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardElevation="6dp"
            app:cardCornerRadius="16dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_student_management"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:text="学生管理"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:gravity="center"
                app:icon="@drawable/ic_student"
                app:iconGravity="start"
                app:iconPadding="20dp"
                app:iconSize="32dp"
                app:iconTint="@android:color/white"
                style="@style/Widget.Material3.Button.UnelevatedButton"
                android:backgroundTint="@color/button_primary_light" />

        </com.google.android.material.card.MaterialCardView>

        <!-- 课程管理按钮 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardElevation="6dp"
            app:cardCornerRadius="16dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_course_management"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:text="课程管理"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:gravity="center"
                app:icon="@drawable/ic_course"
                app:iconGravity="start"
                app:iconPadding="20dp"
                app:iconSize="32dp"
                app:iconTint="@android:color/white"
                style="@style/Widget.Material3.Button.UnelevatedButton"
                android:backgroundTint="@color/button_primary" />

        </com.google.android.material.card.MaterialCardView>

        <!-- 成绩管理按钮 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardElevation="6dp"
            app:cardCornerRadius="16dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_grade_management"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:text="成绩管理"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:gravity="center"
                app:icon="@drawable/ic_grade"
                app:iconGravity="start"
                app:iconPadding="20dp"
                app:iconSize="32dp"
                app:iconTint="@android:color/white"
                style="@style/Widget.Material3.Button.UnelevatedButton"
                android:backgroundTint="@color/button_primary" />

        </com.google.android.material.card.MaterialCardView>

        <!-- 统计分析按钮 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardElevation="6dp"
            app:cardCornerRadius="16dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_statistics"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:text="统计分析"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:gravity="center"
                app:icon="@drawable/ic_statistics"
                app:iconGravity="start"
                app:iconPadding="20dp"
                app:iconSize="32dp"
                app:iconTint="@android:color/white"
                style="@style/Widget.Material3.Button.UnelevatedButton"
                android:backgroundTint="@color/button_success" />

        </com.google.android.material.card.MaterialCardView>

        <!-- 退出登录按钮 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardElevation="6dp"
            app:cardCornerRadius="16dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_logout"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:text="退出登录"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:gravity="center"
                app:icon="@drawable/ic_logout"
                app:iconGravity="start"
                app:iconPadding="20dp"
                app:iconSize="32dp"
                app:iconTint="@android:color/white"
                style="@style/Widget.Material3.Button.UnelevatedButton"
                android:backgroundTint="@color/button_danger" />

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <!-- 底部信息卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        app:cardElevation="2dp"
        app:cardCornerRadius="12dp"
        app:cardBackgroundColor="@color/info_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_info"
                android:layout_marginEnd="8dp"
                app:tint="@color/info_text" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="版本 1.0 | 学生成绩管理系统"
                android:textSize="12sp"
                android:textColor="@color/info_text"
                android:textStyle="bold" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
