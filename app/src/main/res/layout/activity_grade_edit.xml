<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="@color/background_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 学生选择 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="选择学生"
            app:startIconDrawable="@drawable/ic_student"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <Spinner
                android:id="@+id/spinner_student"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 课程选择 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="选择课程"
            app:startIconDrawable="@drawable/ic_course"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <Spinner
                android:id="@+id/spinner_course"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 成绩输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="成绩"
            app:startIconDrawable="@drawable/ic_grade"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_score"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal"
                android:maxLength="5" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 考试类型选择 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="考试类型"
            app:startIconDrawable="@drawable/ic_exam_type"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <Spinner
                android:id="@+id/spinner_exam_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 考试日期输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="考试日期 (YYYY-MM-DD)"
            app:startIconDrawable="@drawable/ic_calendar"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_exam_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="date"
                android:maxLength="10" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 学期选择 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:hint="学期"
            app:startIconDrawable="@drawable/ic_calendar"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <Spinner
                android:id="@+id/spinner_semester"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 提示信息 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/info_background"
            app:cardElevation="2dp"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_info"
                    android:layout_marginEnd="8dp"
                    app:tint="@color/info_text" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="请选择学生和课程，输入成绩（0-100分），选择考试类型和学期。成绩支持小数点。"
                    android:textSize="12sp"
                    android:textColor="@color/info_text" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
