<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardElevation="12dp"
    app:cardCornerRadius="20dp"
    app:cardBackgroundColor="@android:color/transparent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/welcome_toast_background"
        android:padding="20dp"
        android:gravity="center_vertical">

        <!-- 成功图标 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_check_circle"
            android:layout_marginEnd="12dp"
            app:tint="@android:color/white" />

        <!-- 欢迎消息 -->
        <TextView
            android:id="@+id/tv_welcome_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="登录成功，欢迎"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:maxLines="2"
            android:ellipsize="end" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
