<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp"
    app:cardElevation="8dp"
    app:cardCornerRadius="20dp"
    app:cardBackgroundColor="@color/background_card"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 成绩基本信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- 学生头像 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="56dp"
                android:layout_height="56dp"
                app:cardElevation="4dp"
                app:cardCornerRadius="28dp"
                android:layout_marginEnd="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/student_avatar_background"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_person"
                        app:tint="@android:color/white" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_gravity="center_vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="4dp">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_person"
                        android:layout_marginEnd="6dp"
                        app:tint="@color/button_primary" />

                    <TextView
                        android:id="@+id/tv_student_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="学生: 张三"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_text" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_book"
                        android:layout_marginEnd="6dp"
                        app:tint="@color/button_secondary" />

                    <TextView
                        android:id="@+id/tv_course_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="课程: Java程序设计"
                        android:textSize="14sp"
                        android:textColor="@color/secondary_text" />

                </LinearLayout>

            </LinearLayout>

            <!-- 成绩显示 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardElevation="6dp"
                app:cardCornerRadius="20dp"
                app:cardBackgroundColor="@android:color/transparent">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/score_background"
                    android:padding="16dp"
                    android:gravity="center"
                    android:minWidth="80dp"
                    android:minHeight="80dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="成绩"
                        android:textSize="10sp"
                        android:textColor="@android:color/white"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="85.5分"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white"
                        android:gravity="center"
                        android:singleLine="true" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- 详细信息 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="@color/info_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:src="@drawable/ic_exam"
                            android:layout_marginEnd="6dp"
                            app:tint="@color/button_accent" />

                        <TextView
                            android:id="@+id/tv_exam_type"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="考试类型: 期中考试"
                            android:textSize="13sp"
                            android:textColor="@color/secondary_text" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:src="@drawable/ic_calendar"
                            android:layout_marginEnd="6dp"
                            app:tint="@color/button_accent" />

                        <TextView
                            android:id="@+id/tv_exam_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="考试日期: 2024-04-15"
                            android:textSize="13sp"
                            android:textColor="@color/secondary_text" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="14dp"
                        android:layout_height="14dp"
                        android:src="@drawable/ic_school"
                        android:layout_marginEnd="6dp"
                        app:tint="@color/button_accent" />

                    <TextView
                        android:id="@+id/tv_semester"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="学期: 2024春季"
                        android:textSize="13sp"
                        android:textColor="@color/secondary_text" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:layout_marginTop="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_grade"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="编辑"
                android:textSize="13sp"
                android:textStyle="bold"
                android:layout_marginEnd="12dp"
                android:paddingHorizontal="20dp"
                app:icon="@drawable/ic_edit"
                app:iconSize="18dp"
                app:iconTint="@android:color/white"
                app:cornerRadius="20dp"
                app:backgroundTint="@color/button_primary"
                android:textColor="@android:color/white"
                app:elevation="4dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete_grade"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="删除"
                android:textSize="13sp"
                android:textStyle="bold"
                android:paddingHorizontal="20dp"
                app:icon="@drawable/ic_delete"
                app:iconSize="18dp"
                app:iconTint="@android:color/white"
                app:cornerRadius="20dp"
                app:backgroundTint="@color/button_danger"
                android:textColor="@android:color/white"
                app:elevation="4dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
