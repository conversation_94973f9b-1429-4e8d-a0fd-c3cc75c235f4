<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp"
    app:cardElevation="8dp"
    app:cardCornerRadius="20dp"
    app:cardBackgroundColor="@color/background_card"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp"
        android:background="@drawable/card_background">

        <!-- 学生基本信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <!-- 头像占位符 -->
            <ImageView
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:src="@drawable/ic_student_avatar"
                android:background="@drawable/circle_background"
                android:padding="12dp"
                android:layout_marginEnd="16dp"
                app:tint="@color/button_primary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_student_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="姓名: 张三"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color" />

                <TextView
                    android:id="@+id/tv_student_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="学号: 2021001"
                    android:textSize="14sp"
                    android:textColor="@color/button_secondary"
                    android:textStyle="bold"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 详细信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/tv_student_gender"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="性别: 男"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text" />

                <TextView
                    android:id="@+id/tv_student_age"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="年龄: 20"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_student_class"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="班级: 计算机1班"
                android:textSize="14sp"
                android:textColor="@color/secondary_text"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tv_student_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="电话: 13800138001"
                android:textSize="14sp"
                android:textColor="@color/secondary_text"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tv_student_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="邮箱: <EMAIL>"
                android:textSize="14sp"
                android:textColor="@color/secondary_text" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_student"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="编辑"
                android:textSize="12sp"
                android:textColor="@color/button_primary"
                android:layout_marginEnd="12dp"
                app:icon="@drawable/ic_edit"
                app:iconSize="18dp"
                app:iconTint="@color/button_primary"
                style="@style/Widget.Material3.Button.OutlinedButton"
                app:strokeColor="@color/button_primary"
                app:strokeWidth="2dp"
                app:cornerRadius="12dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete_student"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="删除"
                android:textSize="12sp"
                android:textColor="@color/button_danger"
                app:icon="@drawable/ic_delete"
                app:iconSize="18dp"
                app:iconTint="@color/button_danger"
                style="@style/Widget.Material3.Button.OutlinedButton"
                app:strokeColor="@color/button_danger"
                app:strokeWidth="2dp"
                app:cornerRadius="12dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
