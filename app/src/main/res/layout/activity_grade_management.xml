<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/main_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 搜索栏 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardElevation="8dp"
            app:cardCornerRadius="25dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="4dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_search"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="8dp"
                    app:tint="@color/button_primary" />

                <SearchView
                    android:id="@+id/search_view_grades"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:queryHint="搜索成绩（学生或课程）"
                    android:iconifiedByDefault="false"
                    android:searchIcon="@null"
                    android:background="@android:color/transparent" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 成绩列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_grades"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="8dp"
            android:clipToPadding="false" />

        <!-- 空状态提示 -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            android:padding="32dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="140dp"
                android:layout_height="140dp"
                app:cardElevation="8dp"
                app:cardCornerRadius="70dp"
                app:cardBackgroundColor="@color/empty_state_background"
                android:layout_marginBottom="24dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/ic_grade"
                        app:tint="@android:color/white"
                        android:alpha="0.8" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="暂无成绩数据"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/primary_text"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="点击右下角按钮添加成绩"
                android:textSize="14sp"
                android:textColor="@color/secondary_text"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

    <!-- 添加成绩按钮 -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fab_add_grade"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="24dp"
        android:text="添加成绩"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        android:textSize="14sp"
        app:icon="@drawable/ic_add"
        app:iconTint="@android:color/white"
        app:iconSize="24dp"
        app:backgroundTint="@color/fab_gradient_start"
        app:elevation="16dp"
        app:cornerRadius="28dp"
        android:paddingHorizontal="24dp"
        android:paddingVertical="16dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
