<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#E3F2FD"
    android:gravity="center"
    android:padding="48dp">

    <!-- 顶部装饰区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">

        <!-- 应用图标 -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_login_logo"
            android:layout_marginBottom="24dp"
            android:background="@drawable/logo_background"
            android:padding="24dp"
            app:tint="@color/primary_color" />

        <!-- 应用标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="学生成绩管理系统"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/primary_color"
            android:layout_marginBottom="8dp" />

        <!-- 副标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Student Grade Management System"
            android:textSize="14sp"
            android:textColor="@color/button_secondary"
            android:layout_marginBottom="32dp" />

    </LinearLayout>

    <!-- 登录表单卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        app:cardElevation="12dp"
        app:cardCornerRadius="24dp"
        app:cardBackgroundColor="@color/background_white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="32dp">

            <!-- 登录标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="用户登录"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/primary_color"
                android:gravity="center"
                android:layout_marginBottom="32dp" />

            <!-- 用户名输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:hint="用户名"
                app:startIconDrawable="@drawable/ic_person"
                app:startIconTint="@color/button_primary"
                app:boxStrokeColor="@color/button_primary"
                app:hintTextColor="@color/button_primary"
                app:boxCornerRadiusTopStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusBottomEnd="12dp"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_username"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLength="20"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 密码输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="密码"
                app:startIconDrawable="@drawable/ic_lock"
                app:startIconTint="@color/button_primary"
                app:endIconMode="password_toggle"
                app:endIconTint="@color/button_primary"
                app:boxStrokeColor="@color/button_primary"
                app:hintTextColor="@color/button_primary"
                app:boxCornerRadiusTopStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusBottomEnd="12dp"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLength="20"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 登录按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_login"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="登录"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                app:icon="@drawable/ic_login"
                app:iconGravity="textStart"
                app:iconPadding="12dp"
                app:iconSize="24dp"
                app:iconTint="@android:color/white"
                android:background="@drawable/login_button_background"
                app:cornerRadius="16dp"
                style="@style/Widget.Material3.Button.UnelevatedButton" />

            <!-- 提示信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="24dp"
                android:background="@drawable/info_card_background"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试账号"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/info_text"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="管理员: admin / 1227"
                    android:textSize="12sp"
                    android:textColor="@color/info_text"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="学生: a1 / 123456"
                    android:textSize="12sp"
                    android:textColor="@color/info_text" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 底部空间 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.5" />

</LinearLayout>
