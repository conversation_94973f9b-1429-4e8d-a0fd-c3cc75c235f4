<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground">

    <!-- 排名 -->
    <TextView
        android:id="@+id/tv_rank"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="1"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/primary_text"
        android:gravity="center" />

    <!-- 学生姓名 -->
    <TextView
        android:id="@+id/tv_student_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="3"
        android:text="张三"
        android:textSize="16sp"
        android:textColor="@color/primary_text"
        android:gravity="center" />

    <!-- 平均分 -->
    <TextView
        android:id="@+id/tv_average_score"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:text="88.75"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/button_accent"
        android:gravity="center" />

</LinearLayout>
