<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/main_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 搜索栏 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="8dp">

            <SearchView
                android:id="@+id/search_view_courses"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:queryHint="搜索课程（名称或编号）"
                android:iconifiedByDefault="false"
                android:layout_margin="8dp" />

        </com.google.android.material.card.MaterialCardView>

        <!-- 课程列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_courses"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="8dp"
            android:clipToPadding="false" />

        <!-- 空状态提示 -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@drawable/ic_empty_state"
                android:alpha="0.5"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="暂无课程数据"
                android:textSize="16sp"
                android:textColor="@color/secondary_text" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="点击右下角按钮添加课程"
                android:textSize="14sp"
                android:textColor="@color/secondary_text"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 添加课程按钮 -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fab_add_course"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="20dp"
        android:text="添加课程"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        app:icon="@drawable/ic_add"
        app:iconTint="@android:color/white"
        app:backgroundTint="@color/button_secondary"
        app:elevation="12dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
