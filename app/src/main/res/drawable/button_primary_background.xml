<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <gradient
                android:startColor="@color/primary_dark"
                android:endColor="@color/primary_color"
                android:angle="45" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item>
        <shape>
            <gradient
                android:startColor="@color/gradient_blue_start"
                android:endColor="@color/gradient_blue_end"
                android:angle="45" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
