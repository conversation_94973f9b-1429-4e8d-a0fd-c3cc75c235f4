<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 主色调 -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- 蓝白色系主题色 -->
    <color name="primary_color">#1E88E5</color>
    <color name="primary_dark">#1565C0</color>
    <color name="primary_light">#E3F2FD</color>
    <color name="accent_color">#42A5F5</color>
    <color name="accent_light">#BBDEFB</color>

    <!-- 蓝白色系按钮颜色 -->
    <color name="button_primary">#1E88E5</color>
    <color name="button_primary_light">#9C27B0</color>
    <color name="button_secondary">#42A5F5</color>
    <color name="button_accent">#64B5F6</color>
    <color name="button_success">#66BB6A</color>
    <color name="button_danger">#EF5350</color>
    <color name="button_outline">#E1F5FE</color>
    <color name="button_light">#F3F9FF</color>

    <!-- 文本颜色 -->
    <color name="primary_text">#212121</color>
    <color name="secondary_text">#757575</color>
    <color name="hint_text">#BDBDBD</color>

    <!-- 蓝白色系背景颜色 -->
    <color name="background_light">#F8FBFF</color>
    <color name="background_white">#FFFFFF</color>
    <color name="background_gray">#F0F7FF</color>
    <color name="background_card">#FAFCFF</color>
    <color name="background_gradient_start">#E3F2FD</color>
    <color name="background_gradient_end">#FFFFFF</color>

    <!-- 蓝白色系信息提示颜色 -->
    <color name="info_background">#E1F5FE</color>
    <color name="info_text">#0277BD</color>
    <color name="success_background">#E8F5E8</color>
    <color name="success_text">#2E7D32</color>
    <color name="warning_background">#FFF8E1</color>
    <color name="warning_text">#F57C00</color>
    <color name="error_background">#FFEBEE</color>
    <color name="error_text">#C62828</color>

    <!-- 蓝白色系分割线和阴影 -->
    <color name="divider_color">#E1F5FE</color>
    <color name="divider_light">#F0F7FF</color>

    <!-- 卡片阴影 -->
    <color name="card_shadow">#1A1E88E5</color>
    <color name="card_shadow_light">#0D1E88E5</color>

    <!-- 渐变色 -->
    <color name="gradient_blue_start">#1E88E5</color>
    <color name="gradient_blue_end">#42A5F5</color>
    <color name="gradient_light_start">#E3F2FD</color>
    <color name="gradient_light_end">#FFFFFF</color>

    <!-- 空状态和特殊背景 -->
    <color name="empty_state_background">#90CAF9</color>
    <color name="fab_gradient_start">#1E88E5</color>
    <color name="fab_gradient_end">#42A5F5</color>
</resources>