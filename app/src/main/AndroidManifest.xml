<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Material3.DayNight"
        tools:targetApi="31">

        <!-- 登录界面 -->
        <activity
            android:name=".controller.LoginActivity"
            android:exported="true"
            android:label="用户登录"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 主界面 -->
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:label="学生成绩管理系统"
            android:theme="@style/Theme.Material3.DayNight" />

        <!-- 学生管理 -->
        <activity
            android:name=".controller.StudentManagementActivity"
            android:label="学生管理"
            android:parentActivityName=".MainActivity" />

        <!-- 学生编辑 -->
        <activity
            android:name=".controller.StudentEditActivity"
            android:label="学生信息"
            android:parentActivityName=".controller.StudentManagementActivity" />

        <!-- 课程管理 -->
        <activity
            android:name=".controller.CourseManagementActivity"
            android:label="课程管理"
            android:parentActivityName=".MainActivity" />

        <!-- 课程编辑 -->
        <activity
            android:name=".controller.CourseEditActivity"
            android:label="课程信息"
            android:parentActivityName=".controller.CourseManagementActivity" />

        <!-- 成绩管理 -->
        <activity
            android:name=".controller.GradeManagementActivity"
            android:label="成绩管理"
            android:parentActivityName=".MainActivity" />

        <!-- 成绩编辑 -->
        <activity
            android:name=".controller.GradeEditActivity"
            android:label="成绩信息"
            android:parentActivityName=".controller.GradeManagementActivity" />

        <!-- 统计分析 -->
        <activity
            android:name=".controller.StatisticsActivity"
            android:label="统计分析"
            android:parentActivityName=".MainActivity" />

    </application>

</manifest>