# 学生成绩管理系统 - 登录系统使用指南

## 🔐 登录系统概述

本系统实现了完整的用户认证和权限管理功能，支持管理员和学生两种角色，确保数据安全和访问控制。

## 👥 用户角色与权限

### 管理员 (admin)
- **账号**: `admin`
- **密码**: `1227`
- **权限**:
  - ✅ 查看所有学生信息
  - ✅ 管理学生信息（增删改查）
  - ✅ 管理课程信息（增删改查）
  - ✅ 管理所有成绩信息
  - ✅ 查看完整统计分析
  - ✅ 系统管理功能

### 学生 (student)
- **账号**: `a1`
- **密码**: `123456`
- **关联学生**: 张三 (student_id: 1)
- **权限**:
  - ✅ 查看个人信息
  - ✅ 查看个人课程信息
  - ✅ 查看个人成绩信息
  - ✅ 查看个人统计分析
  - ❌ 无法访问课程管理
  - ❌ 无法查看其他学生信息

## 🎨 登录界面设计特色

### 视觉设计
- **配色方案**: 蓝白色系，与整体应用保持一致
- **布局风格**: Material Design 3，现代化卡片设计
- **动画效果**: 流畅的登录和欢迎动画

### 界面元素
1. **应用Logo**: 圆形渐变背景，突出品牌形象
2. **登录表单**: 圆角卡片包装，阴影效果增强层次
3. **输入框**: 带图标的Material Design输入框
4. **登录按钮**: 渐变背景，按压动画效果
5. **测试账号提示**: 方便用户快速测试

### 动画效果
- **按钮动画**: 点击时的缩放效果
- **错误提示**: 输入框震动动画
- **欢迎动画**: 气泡式弹出效果，带成功图标

## 🔧 技术实现

### 数据库设计
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    user_type TEXT NOT NULL,
    real_name TEXT NOT NULL,
    student_id INTEGER DEFAULT 0
);
```

### 会话管理
- 使用 `SharedPreferences` 存储登录状态
- 自动登录检查
- 安全的登出机制

### 权限控制
- 基于角色的访问控制 (RBAC)
- 界面元素动态显示/隐藏
- 数据查询权限过滤

## 📱 使用流程

### 首次启动
1. 应用启动后自动显示登录界面
2. 用户输入账号密码
3. 系统验证用户凭据
4. 登录成功显示欢迎动画
5. 跳转到主界面

### 权限体验
#### 管理员登录后
- 可以看到所有四个功能模块
- 标题显示"欢迎，系统管理员"
- 右上角有退出登录按钮

#### 学生登录后
- 课程管理按钮被隐藏
- 标题显示"欢迎，张三"
- 只能查看与自己相关的数据

### 登出流程
1. 点击右上角退出按钮
2. 清除登录会话
3. 返回登录界面
4. 显示退出成功提示

## 🎯 功能特色

### 1. 安全性
- 密码验证机制
- 会话管理
- 权限控制
- 数据隔离

### 2. 用户体验
- 美观的登录界面
- 流畅的动画效果
- 清晰的权限提示
- 便捷的测试账号

### 3. 权限管理
- 角色基础访问控制
- 数据级权限过滤
- 界面元素权限控制
- 功能模块权限管理

## 🔍 测试场景

### 管理员测试
1. 使用 `admin/1227` 登录
2. 验证可以访问所有功能
3. 测试学生管理、课程管理、成绩管理、统计分析
4. 验证可以查看所有数据

### 学生测试
1. 使用 `a1/123456` 登录
2. 验证课程管理按钮不可见
3. 测试学生信息查看（仅自己）
4. 测试成绩查看（仅自己）
5. 测试统计分析（仅自己）

### 登录验证测试
1. 测试错误密码登录
2. 测试空用户名/密码
3. 测试登录动画效果
4. 测试欢迎动画效果
5. 测试自动登录功能

## 📊 数据关联

### 用户-学生关联
- 管理员用户 (admin) → 无关联学生 (student_id: 0)
- 学生用户 (a1) → 关联张三 (student_id: 1)

### 权限数据过滤
```java
// 学生只能查看自己的成绩
if (sessionManager.isStudent()) {
    int studentId = sessionManager.getCurrentStudentId();
    grades = gradeDAO.getGradesByStudentId(studentId);
}
```

## 🎉 登录系统优势

1. **完整的权限体系**: 支持多角色，权限控制精确
2. **美观的界面设计**: 蓝白配色，现代化设计
3. **流畅的动画效果**: 提升用户体验
4. **安全的会话管理**: 自动登录，安全登出
5. **便捷的测试功能**: 内置测试账号，快速体验

---

**登录系统版本**: v1.0  
**支持角色**: 管理员、学生  
**安全等级**: 基础认证 + 权限控制  
**界面风格**: Material Design 3 + 蓝白配色
