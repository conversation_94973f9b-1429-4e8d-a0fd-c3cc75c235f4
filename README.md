# 学生成绩统计管理系统

一个基于Android平台的学生成绩管理应用，采用Java语言开发，使用SQLite数据库，遵循MVC架构模式。

## 功能特性

### 核心功能模块
1. **学生管理模块**
   - 学生信息的增删改查
   - 支持按姓名、学号搜索
   - 输入验证和数据完整性检查

2. **课程管理模块**
   - 课程信息的增删改查
   - 课程编号唯一性验证
   - 学期和教师信息管理

3. **成绩管理模块**
   - 成绩录入、修改、删除
   - 多表联动查询（学生-课程-成绩）
   - 支持多种考试类型

4. **统计分析模块**
   - 学生成绩排名
   - 成绩分布统计图表
   - 平均分计算

### 技术特性
- **MVC架构**: 清晰的模块划分，便于维护
- **SQLite数据库**: 本地数据存储，支持复杂查询
- **Material Design**: 现代化UI设计
- **输入验证**: 完善的数据验证机制
- **多表联动**: 实现数据表间的关联操作

## 技术架构

### 数据库设计
- **学生表 (students)**: 存储学生基本信息
- **课程表 (courses)**: 存储课程信息
- **成绩表 (grades)**: 存储成绩记录，关联学生和课程

### 项目结构
```
app/src/main/java/com/example/studentgrademanager/
├── model/              # 数据模型层
│   ├── Student.java
│   ├── Course.java
│   └── Grade.java
├── dao/                # 数据访问层
│   ├── StudentDAO.java
│   ├── CourseDAO.java
│   └── GradeDAO.java
├── controller/         # 控制器层
│   ├── MainActivity.java
│   ├── StudentManagementActivity.java
│   ├── CourseManagementActivity.java
│   ├── GradeManagementActivity.java
│   └── StatisticsActivity.java
├── adapter/            # 适配器层
│   ├── StudentAdapter.java
│   ├── CourseAdapter.java
│   ├── GradeAdapter.java
│   └── StatisticsAdapter.java
└── database/           # 数据库层
    └── DatabaseHelper.java
```

## 功能截图说明

### 主界面
- 四个主要功能模块入口
- Material Design风格的按钮设计
- 清晰的功能分类

### 学生管理
- 学生列表展示
- 搜索功能
- 添加/编辑学生信息
- 输入验证（学号唯一性、邮箱格式等）

### 课程管理
- 课程信息管理
- 学期选择
- 学分设置
- 课程描述

### 成绩管理
- 学生和课程下拉选择
- 成绩录入（支持小数）
- 考试类型分类
- 日期选择

### 统计分析
- 柱状图显示学生排名
- 饼图显示成绩分布
- 详细排名列表

## 安装和运行

### 环境要求
- Android Studio 4.0+
- Android SDK API 24+
- Java 11+

### 安装步骤
1. 克隆项目到本地
2. 使用Android Studio打开项目
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击运行按钮

### 构建命令
```bash
# 构建Debug版本
./gradlew assembleDebug

# 运行测试
./gradlew test

# 清理项目
./gradlew clean
```

## 数据库初始化

应用首次启动时会自动创建数据库并插入示例数据：
- 3个示例学生
- 3门示例课程
- 4条示例成绩记录

## 主要依赖

- **Android Support Libraries**: UI组件和兼容性
- **Material Design Components**: 现代化UI设计
- **MPAndroidChart**: 图表显示库
- **RecyclerView**: 列表展示
- **CardView**: 卡片式布局

## 开发规范

### 代码规范
- 遵循Java命名规范
- 完整的注释文档
- 异常处理机制
- 资源文件规范命名

### 数据库规范
- 外键约束
- 数据完整性检查
- 事务处理
- SQL注入防护

## 测试

项目包含单元测试，验证：
- 数据模型的正确性
- 基本功能的完整性
- 数据验证逻辑

运行测试：
```bash
./gradlew test
```

## 版本信息

- **版本**: 1.0
- **最低Android版本**: API 24 (Android 7.0)
- **目标Android版本**: API 35
- **开发语言**: Java
- **数据库**: SQLite

## 作者

学生成绩统计管理系统 - Android应用开发项目

## 许可证

本项目仅用于学习和教育目的。
